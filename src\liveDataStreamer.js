/**
 * نظام البث المباشر للأسعار ونسب الأرباح
 * Live Data Streaming System for Prices and Profit Rates
 */

const WebSocket = require('ws');
const EventEmitter = require('events');

class LiveDataStreamer extends EventEmitter {
    constructor(quotexConnector, options = {}) {
        super();
        
        this.quotexConnector = quotexConnector;
        this.options = {
            port: options.port || 8080,
            maxConnections: options.maxConnections || 100,
            heartbeatInterval: options.heartbeatInterval || 30000,
            dataUpdateInterval: options.dataUpdateInterval || 1000,
            enableCompression: options.enableCompression !== false,
            enableAuth: options.enableAuth || false,
            ...options
        };

        // WebSocket Server
        this.wss = null;
        this.clients = new Set();
        
        // بيانات البث المباشر
        this.liveData = {
            prices: new Map(), // assetId -> price data
            profitRates: new Map(), // assetId -> profit rate
            candles: new Map(), // assetId -> current candle
            lastUpdate: null
        };

        // قائمة الأزواج المستهدفة
        this.targetPairs = [];
        
        // مؤقتات
        this.heartbeatTimer = null;
        this.dataUpdateTimer = null;
        
        // إحصائيات
        this.stats = {
            connectedClients: 0,
            totalMessages: 0,
            dataUpdates: 0,
            errors: 0,
            startTime: null
        };

        this.isRunning = false;
    }

    /**
     * بدء نظام البث المباشر
     */
    async start() {
        try {
            console.log('🚀 Starting Live Data Streamer...');
            
            // إنشاء WebSocket Server
            this.wss = new WebSocket.Server({
                port: this.options.port,
                perMessageDeflate: this.options.enableCompression
            });

            // إعداد معالجات الأحداث
            this.setupWebSocketHandlers();
            this.setupQuotexHandlers();
            
            // تحميل قائمة الأزواج
            this.targetPairs = this.quotexConnector.getTargetPairs();
            
            // بدء المؤقتات
            this.startHeartbeat();
            this.startDataUpdates();
            
            this.isRunning = true;
            this.stats.startTime = new Date();
            
            console.log(`✅ Live Data Streamer started on port ${this.options.port}`);
            this.emit('started');
            
            return true;
            
        } catch (error) {
            console.error('❌ Failed to start Live Data Streamer:', error);
            this.emit('error', error);
            throw error;
        }
    }

    /**
     * إعداد معالجات WebSocket
     */
    setupWebSocketHandlers() {
        this.wss.on('connection', (ws, request) => {
            console.log('🔗 New client connected');
            
            // إضافة العميل
            this.clients.add(ws);
            this.stats.connectedClients = this.clients.size;
            
            // إرسال البيانات الأولية
            this.sendInitialData(ws);
            
            // معالج الرسائل الواردة
            ws.on('message', (message) => {
                this.handleClientMessage(ws, message);
            });
            
            // معالج قطع الاتصال
            ws.on('close', () => {
                console.log('🔌 Client disconnected');
                this.clients.delete(ws);
                this.stats.connectedClients = this.clients.size;
            });
            
            // معالج الأخطاء
            ws.on('error', (error) => {
                console.error('❌ WebSocket error:', error);
                this.clients.delete(ws);
                this.stats.errors++;
            });
        });

        this.wss.on('error', (error) => {
            console.error('❌ WebSocket Server error:', error);
            this.emit('error', error);
        });
    }

    /**
     * إعداد معالجات Quotex
     */
    setupQuotexHandlers() {
        // معالج تحديث الأسعار
        this.quotexConnector.on('priceUpdate', (data) => {
            this.updatePrice(data.assetId, data);
        });

        // معالج تحديث نسب الأرباح
        this.quotexConnector.on('profitRateUpdate', (data) => {
            this.updateProfitRate(data.assetId, data.rate);
        });

        // معالج تحديث الشموع
        this.quotexConnector.on('candleUpdate', (data) => {
            this.updateCandle(data.assetId, data);
        });

        // معالج تحديث الرصيد
        this.quotexConnector.on('balanceUpdate', (data) => {
            this.broadcastMessage('balance_update', data);
        });

        // معالج نتائج الصفقات
        this.quotexConnector.on('tradeResult', (data) => {
            this.broadcastMessage('trade_result', data);
        });
    }

    /**
     * إرسال البيانات الأولية للعميل الجديد
     */
    sendInitialData(ws) {
        try {
            // إرسال قائمة الأزواج
            this.sendMessage(ws, 'pairs_list', {
                pairs: this.targetPairs,
                timestamp: new Date().toISOString()
            });

            // إرسال الأسعار الحالية
            const currentPrices = {};
            for (const [assetId, priceData] of this.liveData.prices) {
                currentPrices[assetId] = priceData;
            }
            
            this.sendMessage(ws, 'prices_snapshot', {
                prices: currentPrices,
                timestamp: new Date().toISOString()
            });

            // إرسال نسب الأرباح الحالية
            const currentRates = {};
            for (const [assetId, rate] of this.liveData.profitRates) {
                currentRates[assetId] = rate;
            }
            
            this.sendMessage(ws, 'profit_rates_snapshot', {
                rates: currentRates,
                timestamp: new Date().toISOString()
            });

            // إرسال الشموع الحالية
            const currentCandles = {};
            for (const [assetId, candle] of this.liveData.candles) {
                currentCandles[assetId] = candle;
            }
            
            this.sendMessage(ws, 'candles_snapshot', {
                candles: currentCandles,
                timestamp: new Date().toISOString()
            });

        } catch (error) {
            console.error('❌ Error sending initial data:', error);
        }
    }

    /**
     * معالجة رسائل العميل
     */
    handleClientMessage(ws, message) {
        try {
            const data = JSON.parse(message);
            
            switch (data.type) {
                case 'subscribe':
                    this.handleSubscription(ws, data.payload);
                    break;
                    
                case 'unsubscribe':
                    this.handleUnsubscription(ws, data.payload);
                    break;
                    
                case 'ping':
                    this.sendMessage(ws, 'pong', { timestamp: new Date().toISOString() });
                    break;
                    
                case 'get_stats':
                    this.sendMessage(ws, 'stats', this.getStats());
                    break;
                    
                default:
                    console.warn('⚠️ Unknown message type:', data.type);
            }
            
        } catch (error) {
            console.error('❌ Error handling client message:', error);
            this.sendMessage(ws, 'error', { message: 'Invalid message format' });
        }
    }

    /**
     * تحديث السعر
     */
    updatePrice(assetId, priceData) {
        const updatedData = {
            assetId: assetId,
            price: priceData.price,
            change: priceData.change || 0,
            changePercent: priceData.changePercent || 0,
            timestamp: new Date().toISOString(),
            ...priceData
        };

        this.liveData.prices.set(assetId, updatedData);
        this.liveData.lastUpdate = new Date().toISOString();
        
        // بث التحديث للعملاء
        this.broadcastMessage('price_update', updatedData);
        this.stats.dataUpdates++;
    }

    /**
     * تحديث نسبة الربح
     */
    updateProfitRate(assetId, rate) {
        const updatedData = {
            assetId: assetId,
            rate: rate,
            timestamp: new Date().toISOString()
        };

        this.liveData.profitRates.set(assetId, updatedData);
        this.liveData.lastUpdate = new Date().toISOString();
        
        // بث التحديث للعملاء
        this.broadcastMessage('profit_rate_update', updatedData);
        this.stats.dataUpdates++;
    }

    /**
     * تحديث الشمعة
     */
    updateCandle(assetId, candleData) {
        const updatedData = {
            assetId: assetId,
            ...candleData,
            timestamp: new Date().toISOString()
        };

        this.liveData.candles.set(assetId, updatedData);
        this.liveData.lastUpdate = new Date().toISOString();
        
        // بث التحديث للعملاء
        this.broadcastMessage('candle_update', updatedData);
        this.stats.dataUpdates++;
    }

    /**
     * بث رسالة لجميع العملاء
     */
    broadcastMessage(type, payload) {
        const message = {
            type: type,
            payload: payload,
            timestamp: new Date().toISOString()
        };

        const messageStr = JSON.stringify(message);
        
        this.clients.forEach(client => {
            if (client.readyState === WebSocket.OPEN) {
                try {
                    client.send(messageStr);
                    this.stats.totalMessages++;
                } catch (error) {
                    console.error('❌ Error sending message to client:', error);
                    this.clients.delete(client);
                }
            }
        });
    }

    /**
     * إرسال رسالة لعميل واحد
     */
    sendMessage(ws, type, payload) {
        if (ws.readyState === WebSocket.OPEN) {
            const message = {
                type: type,
                payload: payload,
                timestamp: new Date().toISOString()
            };

            try {
                ws.send(JSON.stringify(message));
                this.stats.totalMessages++;
            } catch (error) {
                console.error('❌ Error sending message:', error);
                this.clients.delete(ws);
            }
        }
    }

    /**
     * بدء نبضات القلب
     */
    startHeartbeat() {
        this.heartbeatTimer = setInterval(() => {
            this.broadcastMessage('heartbeat', {
                timestamp: new Date().toISOString(),
                connectedClients: this.clients.size
            });
        }, this.options.heartbeatInterval);
    }

    /**
     * بدء تحديثات البيانات
     */
    startDataUpdates() {
        this.dataUpdateTimer = setInterval(async () => {
            try {
                // طلب تحديث البيانات من Quotex
                await this.requestDataUpdates();
            } catch (error) {
                console.error('❌ Error in data update cycle:', error);
            }
        }, this.options.dataUpdateInterval);
    }

    /**
     * طلب تحديث البيانات
     */
    async requestDataUpdates() {
        try {
            // طلب تحديث نسب الأرباح لجميع الأزواج
            for (const pair of this.targetPairs) {
                const profitRate = this.quotexConnector.getProfitRate(pair.id);
                if (profitRate !== null) {
                    this.updateProfitRate(pair.id, profitRate);
                }
            }
            
        } catch (error) {
            console.error('❌ Error requesting data updates:', error);
        }
    }

    /**
     * الحصول على الإحصائيات
     */
    getStats() {
        const uptime = this.stats.startTime ? 
            (new Date() - this.stats.startTime) / 1000 : 0;

        return {
            ...this.stats,
            uptime: Math.round(uptime),
            isRunning: this.isRunning,
            dataPoints: {
                prices: this.liveData.prices.size,
                profitRates: this.liveData.profitRates.size,
                candles: this.liveData.candles.size
            },
            lastUpdate: this.liveData.lastUpdate
        };
    }

    /**
     * إيقاف نظام البث
     */
    async stop() {
        try {
            console.log('🛑 Stopping Live Data Streamer...');
            
            this.isRunning = false;
            
            // إيقاف المؤقتات
            if (this.heartbeatTimer) {
                clearInterval(this.heartbeatTimer);
                this.heartbeatTimer = null;
            }
            
            if (this.dataUpdateTimer) {
                clearInterval(this.dataUpdateTimer);
                this.dataUpdateTimer = null;
            }
            
            // إغلاق جميع الاتصالات
            this.clients.forEach(client => {
                if (client.readyState === WebSocket.OPEN) {
                    client.close();
                }
            });
            this.clients.clear();
            
            // إغلاق الخادم
            if (this.wss) {
                this.wss.close();
            }
            
            console.log('✅ Live Data Streamer stopped');
            this.emit('stopped');
            
        } catch (error) {
            console.error('❌ Error stopping Live Data Streamer:', error);
        }
    }

    /**
     * معالجة الاشتراك
     */
    handleSubscription(ws, payload) {
        // يمكن إضافة منطق الاشتراك المخصص هنا
        this.sendMessage(ws, 'subscription_confirmed', payload);
    }

    /**
     * معالجة إلغاء الاشتراك
     */
    handleUnsubscription(ws, payload) {
        // يمكن إضافة منطق إلغاء الاشتراك المخصص هنا
        this.sendMessage(ws, 'unsubscription_confirmed', payload);
    }
}

module.exports = LiveDataStreamer;
