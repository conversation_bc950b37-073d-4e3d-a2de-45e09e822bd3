{"passed": 7, "failed": 9, "total": 16, "details": [{"name": "Library Instance Creation", "passed": true, "message": "Library instance created successfully", "timestamp": "2025-07-04T19:46:23.994Z"}, {"name": "Library Initialization", "passed": false, "message": "this.storage.initialize is not a function", "timestamp": "2025-07-04T19:46:23.996Z"}, {"name": "Connection Test", "passed": false, "message": "this.connector.getStatus is not a function", "timestamp": "2025-07-04T19:46:23.997Z"}, {"name": "Target Pairs Retrieval", "passed": true, "message": "Target pairs retrieved", "timestamp": "2025-07-04T19:46:23.998Z"}, {"name": "Pairs Count", "passed": true, "message": "Expected 70 pairs, got 70", "timestamp": "2025-07-04T19:46:23.999Z"}, {"name": "Pair Data Structure", "passed": "major", "message": "Pairs have required fields", "timestamp": "2025-07-04T19:46:23.999Z"}, {"name": "Pair Categories", "passed": true, "message": "All expected categories present", "timestamp": "2025-07-04T19:46:23.999Z"}, {"name": "OTC Pairs", "passed": true, "message": "Found 49 OTC pairs", "timestamp": "2025-07-04T19:46:24.000Z"}, {"name": "Regular Pairs", "passed": true, "message": "Found 21 regular pairs", "timestamp": "2025-07-04T19:46:24.000Z"}, {"name": "Historical Manager Exists", "passed": false, "message": "Historical data manager initialized", "timestamp": "2025-07-04T19:46:24.001Z"}, {"name": "Analysis Engine Exists", "passed": false, "message": "Analysis engine initialized", "timestamp": "2025-07-04T19:46:24.001Z"}, {"name": "Auto Trader Exists", "passed": false, "message": "Auto trader initialized", "timestamp": "2025-07-04T19:46:24.002Z"}, {"name": "Live Streamer Exists", "passed": false, "message": "Live data streamer initialized", "timestamp": "2025-07-04T19:46:24.002Z"}, {"name": "Session Manager Exists", "passed": false, "message": "Session manager initialized", "timestamp": "2025-07-04T19:46:24.003Z"}, {"name": "Trade Manager Exists", "passed": false, "message": "Trade manager initialized", "timestamp": "2025-07-04T19:46:24.003Z"}, {"name": "Candle Manager Exists", "passed": false, "message": "Candle manager initialized", "timestamp": "2025-07-04T19:46:24.004Z"}]}