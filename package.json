{"name": "quotex-smart-trading-system", "version": "2.0.0", "description": "Advanced Smart Trading System for Quotex with AI-powered analysis, risk management, and real-time web interface", "main": "main.js", "scripts": {"start": "node start.js", "dev": "nodemon start.js", "main": "node main.js", "test": "node tests/system-test.js", "install-deps": "npm install express socket.io puppeteer dotenv nodemon", "setup": "npm install && node setup.js"}, "keywords": ["quotex", "websocket", "api", "binary options", "trading", "smart trading", "ai trading", "technical analysis", "risk management", "real-time", "web interface"], "author": "Advanced Trading Systems", "license": "ISC", "dependencies": {"dotenv": "^16.6.1", "events": "^3.3.0", "express": "^4.18.2", "puppeteer": "^21.11.0", "socket.io": "^4.7.2", "ws": "^8.18.3"}, "devDependencies": {"nodemon": "^3.0.1"}, "engines": {"node": ">=14.0.0"}}