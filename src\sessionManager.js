/**
 * مدير الجلسات والكوكيز لمنصة Quotex
 * Session and Cookie Manager for Quotex Platform
 */

const fs = require('fs').promises;
const path = require('path');
const EventEmitter = require('events');

class SessionManager extends EventEmitter {
    constructor(options = {}) {
        super();
        
        this.options = {
            sessionDir: options.sessionDir || './sessions',
            cookieFile: options.cookieFile || 'quotex_cookies.json',
            sessionFile: options.sessionFile || 'quotex_session.json',
            maxSessionAge: options.maxSessionAge || 24 * 60 * 60 * 1000, // 24 ساعة
            autoSave: options.autoSave !== false,
            encryptCookies: options.encryptCookies || false,
            ...options
        };

        this.currentSession = null;
        this.cookies = new Map();
        this.sessionData = {};
        this.isInitialized = false;
    }

    /**
     * تهيئة مدير الجلسات
     */
    async initialize() {
        try {
            console.log('🔐 Initializing Session Manager...');
            
            // إنشاء مجلد الجلسات
            await this.ensureSessionDirectory();
            
            // تحميل الجلسة المحفوظة
            await this.loadSavedSession();
            
            // تحميل الكوكيز المحفوظة
            await this.loadSavedCookies();
            
            this.isInitialized = true;
            console.log('✅ Session Manager initialized successfully');
            this.emit('initialized');
            
            return true;
            
        } catch (error) {
            console.error('❌ Failed to initialize Session Manager:', error);
            this.emit('error', error);
            throw error;
        }
    }

    /**
     * التأكد من وجود مجلد الجلسات
     */
    async ensureSessionDirectory() {
        try {
            await fs.access(this.options.sessionDir);
        } catch {
            await fs.mkdir(this.options.sessionDir, { recursive: true });
            console.log(`📁 Created session directory: ${this.options.sessionDir}`);
        }
    }

    /**
     * حفظ جلسة جديدة
     */
    async saveSession(sessionData) {
        try {
            const session = {
                id: this.generateSessionId(),
                email: sessionData.email,
                userId: sessionData.userId,
                accountType: sessionData.accountType || 'demo',
                balance: sessionData.balance || 0,
                loginTime: new Date().toISOString(),
                lastActivity: new Date().toISOString(),
                isActive: true,
                userAgent: sessionData.userAgent,
                ipAddress: sessionData.ipAddress,
                metadata: sessionData.metadata || {}
            };

            this.currentSession = session;
            this.sessionData = { ...sessionData };

            // حفظ الجلسة في ملف
            const sessionPath = path.join(this.options.sessionDir, this.options.sessionFile);
            await this.writeJsonFile(sessionPath, session);

            console.log(`💾 Session saved: ${session.id}`);
            this.emit('sessionSaved', session);

            return session;

        } catch (error) {
            console.error('❌ Error saving session:', error);
            this.emit('error', error);
            throw error;
        }
    }

    /**
     * تحميل الجلسة المحفوظة
     */
    async loadSavedSession() {
        try {
            const sessionPath = path.join(this.options.sessionDir, this.options.sessionFile);
            const session = await this.readJsonFile(sessionPath);

            if (session && this.isSessionValid(session)) {
                this.currentSession = session;
                console.log(`🔓 Loaded valid session: ${session.id}`);
                this.emit('sessionLoaded', session);
                return session;
            } else if (session) {
                console.log('⚠️ Session expired, will create new session');
                await this.clearSession();
            }

            return null;

        } catch (error) {
            console.log('📝 No saved session found, will create new session');
            return null;
        }
    }

    /**
     * حفظ الكوكيز
     */
    async saveCookies(cookies) {
        try {
            const cookieData = {
                timestamp: new Date().toISOString(),
                cookies: Array.isArray(cookies) ? cookies : this.mapToArray(cookies),
                sessionId: this.currentSession?.id,
                domain: 'qxbroker.com',
                metadata: {
                    userAgent: this.currentSession?.userAgent,
                    count: Array.isArray(cookies) ? cookies.length : cookies.size
                }
            };

            // تحديث الكوكيز في الذاكرة
            if (Array.isArray(cookies)) {
                cookies.forEach(cookie => {
                    this.cookies.set(cookie.name, cookie);
                });
            } else {
                this.cookies = new Map(cookies);
            }

            // حفظ الكوكيز في ملف
            const cookiePath = path.join(this.options.sessionDir, this.options.cookieFile);
            await this.writeJsonFile(cookiePath, cookieData);

            console.log(`🍪 Saved ${cookieData.metadata.count} cookies`);
            this.emit('cookiesSaved', cookieData);

            return cookieData;

        } catch (error) {
            console.error('❌ Error saving cookies:', error);
            this.emit('error', error);
            throw error;
        }
    }

    /**
     * تحميل الكوكيز المحفوظة
     */
    async loadSavedCookies() {
        try {
            const cookiePath = path.join(this.options.sessionDir, this.options.cookieFile);
            const cookieData = await this.readJsonFile(cookiePath);

            if (cookieData && this.areCookiesValid(cookieData)) {
                // تحويل الكوكيز إلى Map
                cookieData.cookies.forEach(cookie => {
                    this.cookies.set(cookie.name, cookie);
                });

                console.log(`🍪 Loaded ${this.cookies.size} valid cookies`);
                this.emit('cookiesLoaded', cookieData);
                return cookieData;
            } else if (cookieData) {
                console.log('⚠️ Cookies expired, will collect new cookies');
                await this.clearCookies();
            }

            return null;

        } catch (error) {
            console.log('📝 No saved cookies found, will collect new cookies');
            return null;
        }
    }

    /**
     * الحصول على الكوكيز للمتصفح
     */
    getCookiesForBrowser() {
        return Array.from(this.cookies.values());
    }

    /**
     * الحصول على الجلسة الحالية
     */
    getCurrentSession() {
        return this.currentSession;
    }

    /**
     * تحديث نشاط الجلسة
     */
    async updateSessionActivity(activityData = {}) {
        if (!this.currentSession) return;

        try {
            this.currentSession.lastActivity = new Date().toISOString();
            
            if (activityData.balance !== undefined) {
                this.currentSession.balance = activityData.balance;
            }

            if (activityData.metadata) {
                this.currentSession.metadata = {
                    ...this.currentSession.metadata,
                    ...activityData.metadata
                };
            }

            // حفظ التحديث
            if (this.options.autoSave) {
                const sessionPath = path.join(this.options.sessionDir, this.options.sessionFile);
                await this.writeJsonFile(sessionPath, this.currentSession);
            }

            this.emit('sessionUpdated', this.currentSession);

        } catch (error) {
            console.error('❌ Error updating session activity:', error);
        }
    }

    /**
     * التحقق من صحة الجلسة
     */
    isSessionValid(session) {
        if (!session || !session.loginTime) return false;

        const loginTime = new Date(session.loginTime);
        const now = new Date();
        const sessionAge = now - loginTime;

        return sessionAge < this.options.maxSessionAge && session.isActive;
    }

    /**
     * التحقق من صحة الكوكيز
     */
    areCookiesValid(cookieData) {
        if (!cookieData || !cookieData.timestamp) return false;

        const cookieTime = new Date(cookieData.timestamp);
        const now = new Date();
        const cookieAge = now - cookieTime;

        // الكوكيز صالحة لمدة 12 ساعة
        return cookieAge < (12 * 60 * 60 * 1000);
    }

    /**
     * مسح الجلسة
     */
    async clearSession() {
        try {
            this.currentSession = null;
            this.sessionData = {};

            const sessionPath = path.join(this.options.sessionDir, this.options.sessionFile);
            await fs.unlink(sessionPath).catch(() => {});

            console.log('🗑️ Session cleared');
            this.emit('sessionCleared');

        } catch (error) {
            console.error('❌ Error clearing session:', error);
        }
    }

    /**
     * مسح الكوكيز
     */
    async clearCookies() {
        try {
            this.cookies.clear();

            const cookiePath = path.join(this.options.sessionDir, this.options.cookieFile);
            await fs.unlink(cookiePath).catch(() => {});

            console.log('🗑️ Cookies cleared');
            this.emit('cookiesCleared');

        } catch (error) {
            console.error('❌ Error clearing cookies:', error);
        }
    }

    /**
     * مسح جميع بيانات الجلسة
     */
    async clearAll() {
        await this.clearSession();
        await this.clearCookies();
        console.log('🧹 All session data cleared');
        this.emit('allCleared');
    }

    /**
     * توليد معرف جلسة فريد
     */
    generateSessionId() {
        const timestamp = Date.now();
        const random = Math.random().toString(36).substring(2);
        return `quotex_${timestamp}_${random}`;
    }

    /**
     * تحويل Map إلى Array
     */
    mapToArray(map) {
        return Array.from(map.entries()).map(([key, value]) => ({ name: key, ...value }));
    }

    /**
     * كتابة ملف JSON
     */
    async writeJsonFile(filepath, data) {
        const jsonData = JSON.stringify(data, null, 2);
        await fs.writeFile(filepath, jsonData, 'utf8');
    }

    /**
     * قراءة ملف JSON
     */
    async readJsonFile(filepath) {
        const data = await fs.readFile(filepath, 'utf8');
        return JSON.parse(data);
    }

    /**
     * الحصول على إحصائيات الجلسة
     */
    getSessionStats() {
        return {
            hasActiveSession: !!this.currentSession,
            sessionId: this.currentSession?.id,
            sessionAge: this.currentSession ? 
                Date.now() - new Date(this.currentSession.loginTime).getTime() : 0,
            cookiesCount: this.cookies.size,
            lastActivity: this.currentSession?.lastActivity,
            isInitialized: this.isInitialized
        };
    }
}

module.exports = SessionManager;
