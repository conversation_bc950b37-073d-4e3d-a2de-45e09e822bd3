/**
 * مدير الصفقات المتكامل
 * Comprehensive Trade Manager
 */

const fs = require('fs').promises;
const path = require('path');
const EventEmitter = require('events');

class TradeManager extends EventEmitter {
    constructor(options = {}) {
        super();
        
        this.options = {
            tradesDir: options.tradesDir || './data/trades',
            openTradesFile: options.openTradesFile || 'open_trades.json',
            closedTradesFile: options.closedTradesFile || 'closed_trades.json',
            dailyTradesFile: options.dailyTradesFile || 'daily_trades.json',
            maxTradesPerFile: options.maxTradesPerFile || 1000,
            autoSave: options.autoSave !== false,
            enableBackup: options.enableBackup !== false,
            ...options
        };

        // تخزين الصفقات في الذاكرة
        this.openTrades = new Map();
        this.closedTrades = new Map();
        this.dailyStats = {
            date: new Date().toISOString().split('T')[0],
            totalTrades: 0,
            winTrades: 0,
            lossTrades: 0,
            totalProfit: 0,
            totalLoss: 0,
            winRate: 0
        };

        // إحصائيات
        this.stats = {
            tradesCreated: 0,
            tradesCompleted: 0,
            lastTradeTime: null,
            lastSave: null
        };

        this.isInitialized = false;
    }

    /**
     * تهيئة مدير الصفقات
     */
    async initialize() {
        try {
            console.log('📊 Initializing Trade Manager...');
            
            // إنشاء مجلد الصفقات
            await this.ensureTradesDirectory();
            
            // تحميل الصفقات المفتوحة
            await this.loadOpenTrades();
            
            // تحميل الإحصائيات اليومية
            await this.loadDailyStats();
            
            this.isInitialized = true;
            console.log('✅ Trade Manager initialized successfully');
            this.emit('initialized');
            
            return true;
            
        } catch (error) {
            console.error('❌ Failed to initialize Trade Manager:', error);
            this.emit('error', error);
            throw error;
        }
    }

    /**
     * التأكد من وجود مجلد الصفقات
     */
    async ensureTradesDirectory() {
        try {
            await fs.access(this.options.tradesDir);
        } catch {
            await fs.mkdir(this.options.tradesDir, { recursive: true });
            console.log(`📁 Created trades directory: ${this.options.tradesDir}`);
        }
    }

    /**
     * إنشاء صفقة جديدة
     */
    async createTrade(tradeData) {
        try {
            const trade = {
                id: this.generateTradeId(),
                assetId: tradeData.assetId,
                assetName: tradeData.assetName || tradeData.symbol,
                direction: tradeData.direction, // 'call' or 'put'
                amount: tradeData.amount,
                duration: tradeData.duration, // بالثواني
                openPrice: tradeData.openPrice,
                openTime: new Date().toISOString(),
                expiryTime: new Date(Date.now() + (tradeData.duration * 1000)).toISOString(),
                status: 'open',
                profitRate: tradeData.profitRate || 0.85,
                expectedProfit: tradeData.amount * (tradeData.profitRate || 0.85),
                
                // بيانات التحليل
                analysis: tradeData.analysis || null,
                confidence: tradeData.confidence || 0,
                strategy: tradeData.strategy || 'hybrid',
                
                // بيانات إضافية
                sessionId: tradeData.sessionId,
                userId: tradeData.userId,
                accountType: tradeData.accountType || 'demo',
                
                // نتائج (ستُملأ عند الإغلاق)
                closePrice: null,
                closeTime: null,
                result: null, // 'win', 'loss', 'tie'
                actualProfit: null,
                
                metadata: {
                    created: new Date().toISOString(),
                    source: 'quotex_connector',
                    version: '1.0'
                }
            };

            // حفظ الصفقة في الذاكرة
            this.openTrades.set(trade.id, trade);
            
            // حفظ في الملف
            await this.saveOpenTrades();
            
            // تحديث الإحصائيات
            this.stats.tradesCreated++;
            this.stats.lastTradeTime = new Date();
            this.dailyStats.totalTrades++;
            
            console.log(`📈 Trade created: ${trade.id} (${trade.direction} ${trade.assetName} $${trade.amount})`);
            this.emit('tradeCreated', trade);
            
            return trade;
            
        } catch (error) {
            console.error('❌ Error creating trade:', error);
            this.emit('error', error);
            throw error;
        }
    }

    /**
     * تحديث صفقة مفتوحة
     */
    async updateTrade(tradeId, updateData) {
        try {
            const trade = this.openTrades.get(tradeId);
            if (!trade) {
                throw new Error(`Trade not found: ${tradeId}`);
            }

            // تحديث البيانات
            Object.assign(trade, updateData);
            trade.metadata.lastUpdate = new Date().toISOString();

            // حفظ التحديث
            await this.saveOpenTrades();
            
            console.log(`📊 Trade updated: ${tradeId}`);
            this.emit('tradeUpdated', trade);
            
            return trade;
            
        } catch (error) {
            console.error('❌ Error updating trade:', error);
            this.emit('error', error);
            throw error;
        }
    }

    /**
     * إغلاق صفقة
     */
    async closeTrade(tradeId, closeData) {
        try {
            const trade = this.openTrades.get(tradeId);
            if (!trade) {
                throw new Error(`Trade not found: ${tradeId}`);
            }

            // تحديث بيانات الإغلاق
            trade.closePrice = closeData.closePrice;
            trade.closeTime = closeData.closeTime || new Date().toISOString();
            trade.result = closeData.result; // 'win', 'loss', 'tie'
            trade.status = 'closed';
            
            // حساب الربح الفعلي
            if (trade.result === 'win') {
                trade.actualProfit = trade.expectedProfit;
                this.dailyStats.winTrades++;
                this.dailyStats.totalProfit += trade.actualProfit;
            } else if (trade.result === 'loss') {
                trade.actualProfit = -trade.amount;
                this.dailyStats.lossTrades++;
                this.dailyStats.totalLoss += trade.amount;
            } else {
                trade.actualProfit = 0; // tie
            }

            // نقل من الصفقات المفتوحة إلى المغلقة
            this.openTrades.delete(tradeId);
            this.closedTrades.set(tradeId, trade);

            // حفظ التحديثات
            await this.saveOpenTrades();
            await this.saveClosedTrades();
            await this.updateDailyStats();

            // تحديث الإحصائيات
            this.stats.tradesCompleted++;
            
            console.log(`🏁 Trade closed: ${tradeId} (${trade.result}) Profit: $${trade.actualProfit}`);
            this.emit('tradeClosed', trade);
            
            return trade;
            
        } catch (error) {
            console.error('❌ Error closing trade:', error);
            this.emit('error', error);
            throw error;
        }
    }

    /**
     * الحصول على صفقة بالمعرف
     */
    getTrade(tradeId) {
        return this.openTrades.get(tradeId) || this.closedTrades.get(tradeId);
    }

    /**
     * الحصول على جميع الصفقات المفتوحة
     */
    getOpenTrades() {
        return Array.from(this.openTrades.values());
    }

    /**
     * الحصول على الصفقات المغلقة
     */
    getClosedTrades(limit = 100) {
        const trades = Array.from(this.closedTrades.values());
        return trades.slice(-limit).reverse(); // الأحدث أولاً
    }

    /**
     * الحصول على الصفقات حسب الأصل
     */
    getTradesByAsset(assetId) {
        const openTrades = this.getOpenTrades().filter(t => t.assetId === assetId);
        const closedTrades = this.getClosedTrades().filter(t => t.assetId === assetId);
        return { openTrades, closedTrades };
    }

    /**
     * تحميل الصفقات المفتوحة
     */
    async loadOpenTrades() {
        try {
            const filePath = path.join(this.options.tradesDir, this.options.openTradesFile);
            const data = await this.readJsonFile(filePath);
            
            if (data && data.trades) {
                data.trades.forEach(trade => {
                    this.openTrades.set(trade.id, trade);
                });
                console.log(`📖 Loaded ${this.openTrades.size} open trades`);
            }
            
        } catch (error) {
            console.log('📝 No open trades file found, starting fresh');
        }
    }

    /**
     * حفظ الصفقات المفتوحة
     */
    async saveOpenTrades() {
        try {
            const data = {
                timestamp: new Date().toISOString(),
                count: this.openTrades.size,
                trades: Array.from(this.openTrades.values())
            };

            const filePath = path.join(this.options.tradesDir, this.options.openTradesFile);
            await this.writeJsonFile(filePath, data);
            
            this.stats.lastSave = new Date();
            
        } catch (error) {
            console.error('❌ Error saving open trades:', error);
            throw error;
        }
    }

    /**
     * حفظ الصفقات المغلقة
     */
    async saveClosedTrades() {
        try {
            // تحميل الصفقات المغلقة الموجودة
            let existingData = { trades: [] };
            const filePath = path.join(this.options.tradesDir, this.options.closedTradesFile);
            
            try {
                existingData = await this.readJsonFile(filePath);
            } catch {
                // الملف غير موجود
            }

            // إضافة الصفقات الجديدة
            const newTrades = Array.from(this.closedTrades.values());
            const allTrades = [...existingData.trades, ...newTrades];

            // الاحتفاظ بآخر 1000 صفقة فقط
            const limitedTrades = allTrades.slice(-this.options.maxTradesPerFile);

            const data = {
                timestamp: new Date().toISOString(),
                count: limitedTrades.length,
                trades: limitedTrades
            };

            await this.writeJsonFile(filePath, data);
            
            // مسح الصفقات المغلقة من الذاكرة بعد الحفظ
            this.closedTrades.clear();
            
        } catch (error) {
            console.error('❌ Error saving closed trades:', error);
            throw error;
        }
    }

    /**
     * تحميل الإحصائيات اليومية
     */
    async loadDailyStats() {
        try {
            const today = new Date().toISOString().split('T')[0];
            const fileName = `daily_trades_${today}.json`;
            const filePath = path.join(this.options.tradesDir, fileName);
            
            const data = await this.readJsonFile(filePath);
            if (data) {
                this.dailyStats = { ...this.dailyStats, ...data };
                console.log(`📊 Loaded daily stats: ${this.dailyStats.totalTrades} trades`);
            }
            
        } catch (error) {
            console.log('📝 No daily stats found, starting fresh');
        }
    }

    /**
     * تحديث الإحصائيات اليومية
     */
    async updateDailyStats() {
        try {
            // حساب معدل النجاح
            const totalDecidedTrades = this.dailyStats.winTrades + this.dailyStats.lossTrades;
            this.dailyStats.winRate = totalDecidedTrades > 0 ? 
                (this.dailyStats.winTrades / totalDecidedTrades) * 100 : 0;

            // حفظ الإحصائيات
            const today = new Date().toISOString().split('T')[0];
            const fileName = `daily_trades_${today}.json`;
            const filePath = path.join(this.options.tradesDir, fileName);
            
            await this.writeJsonFile(filePath, this.dailyStats);
            
        } catch (error) {
            console.error('❌ Error updating daily stats:', error);
        }
    }

    /**
     * توليد معرف صفقة فريد
     */
    generateTradeId() {
        const timestamp = Date.now();
        const random = Math.random().toString(36).substring(2, 8);
        return `trade_${timestamp}_${random}`;
    }

    /**
     * كتابة ملف JSON
     */
    async writeJsonFile(filepath, data) {
        const jsonData = JSON.stringify(data, null, 2);
        await fs.writeFile(filepath, jsonData, 'utf8');
    }

    /**
     * قراءة ملف JSON
     */
    async readJsonFile(filepath) {
        const data = await fs.readFile(filepath, 'utf8');
        return JSON.parse(data);
    }

    /**
     * الحصول على إحصائيات مدير الصفقات
     */
    getManagerStats() {
        return {
            ...this.stats,
            openTradesCount: this.openTrades.size,
            closedTradesCount: this.closedTrades.size,
            dailyStats: this.dailyStats,
            isInitialized: this.isInitialized
        };
    }

    /**
     * تنظيف الصفقات المنتهية الصلاحية
     */
    async cleanupExpiredTrades() {
        const now = new Date();
        const expiredTrades = [];

        for (const [tradeId, trade] of this.openTrades) {
            const expiryTime = new Date(trade.expiryTime);
            if (now > expiryTime && trade.status === 'open') {
                expiredTrades.push(tradeId);
            }
        }

        for (const tradeId of expiredTrades) {
            console.log(`⏰ Trade expired: ${tradeId}`);
            await this.closeTrade(tradeId, {
                closePrice: null,
                result: 'expired',
                closeTime: now.toISOString()
            });
        }

        if (expiredTrades.length > 0) {
            console.log(`🧹 Cleaned up ${expiredTrades.length} expired trades`);
        }

        return expiredTrades.length;
    }
}

module.exports = TradeManager;
