/**
 * قائمة الأزواج المستهدفة الـ70 للتداول
 * Target Trading Pairs List (70 pairs)
 */

class TargetPairs {
    constructor() {
        this.pairs = this.initializePairs();
    }

    /**
     * تهيئة قائمة الأزواج الـ70
     */
    initializePairs() {
        return [
            // الأزواج الرئيسية والثانوية (37 زوج)
            { 
                id: 1, symbol: 'GBPUSD', name: 'GBP/USD', 
                category: 'major', isOTC: false, 
                description: 'British Pound vs US Dollar',
                tradingHours: '24/5', volatility: 'medium'
            },
            { 
                id: 2, symbol: 'GBPUSD_otc', name: 'GBP/USD OTC', 
                category: 'major', isOTC: true, 
                description: 'British Pound vs US Dollar OTC',
                tradingHours: '24/7', volatility: 'medium'
            },
            { 
                id: 3, symbol: 'USDJPY', name: 'USD/JPY', 
                category: 'major', isOTC: false, 
                description: 'US Dollar vs Japanese Yen',
                tradingHours: '24/5', volatility: 'medium'
            },
            { 
                id: 4, symbol: 'USDJPY_otc', name: 'USD/JPY OTC', 
                category: 'major', isOTC: true, 
                description: 'US Dollar vs Japanese Yen OTC',
                tradingHours: '24/7', volatility: 'medium'
            },
            { 
                id: 5, symbol: 'CHFJPY', name: 'CHF/JPY', 
                category: 'minor', isOTC: false, 
                description: 'Swiss Franc vs Japanese Yen',
                tradingHours: '24/5', volatility: 'high'
            },
            { 
                id: 6, symbol: 'CHFJPY_otc', name: 'CHF/JPY OTC', 
                category: 'minor', isOTC: true, 
                description: 'Swiss Franc vs Japanese Yen OTC',
                tradingHours: '24/7', volatility: 'high'
            },
            { 
                id: 7, symbol: 'USDCAD', name: 'USD/CAD', 
                category: 'major', isOTC: false, 
                description: 'US Dollar vs Canadian Dollar',
                tradingHours: '24/5', volatility: 'medium'
            },
            { 
                id: 8, symbol: 'USDCAD_otc', name: 'USD/CAD OTC', 
                category: 'major', isOTC: true, 
                description: 'US Dollar vs Canadian Dollar OTC',
                tradingHours: '24/7', volatility: 'medium'
            },
            { 
                id: 9, symbol: 'AUDCAD', name: 'AUD/CAD', 
                category: 'minor', isOTC: false, 
                description: 'Australian Dollar vs Canadian Dollar',
                tradingHours: '24/5', volatility: 'medium'
            },
            { 
                id: 10, symbol: 'AUDCAD_otc', name: 'AUD/CAD OTC', 
                category: 'minor', isOTC: true, 
                description: 'Australian Dollar vs Canadian Dollar OTC',
                tradingHours: '24/7', volatility: 'medium'
            },
            { 
                id: 11, symbol: 'USDCHF', name: 'USD/CHF', 
                category: 'major', isOTC: false, 
                description: 'US Dollar vs Swiss Franc',
                tradingHours: '24/5', volatility: 'medium'
            },
            { 
                id: 12, symbol: 'USDCHF_otc', name: 'USD/CHF OTC', 
                category: 'major', isOTC: true, 
                description: 'US Dollar vs Swiss Franc OTC',
                tradingHours: '24/7', volatility: 'medium'
            },
            { 
                id: 13, symbol: 'EURGBP', name: 'EUR/GBP', 
                category: 'major', isOTC: false, 
                description: 'Euro vs British Pound',
                tradingHours: '24/5', volatility: 'low'
            },
            { 
                id: 14, symbol: 'EURGBP_otc', name: 'EUR/GBP OTC', 
                category: 'major', isOTC: true, 
                description: 'Euro vs British Pound OTC',
                tradingHours: '24/7', volatility: 'low'
            },
            { 
                id: 15, symbol: 'EURAUD', name: 'EUR/AUD', 
                category: 'minor', isOTC: false, 
                description: 'Euro vs Australian Dollar',
                tradingHours: '24/5', volatility: 'medium'
            },
            { 
                id: 16, symbol: 'EURCAD', name: 'EUR/CAD', 
                category: 'minor', isOTC: false, 
                description: 'Euro vs Canadian Dollar',
                tradingHours: '24/5', volatility: 'medium'
            },
            { 
                id: 17, symbol: 'AUDUSD', name: 'AUD/USD', 
                category: 'major', isOTC: false, 
                description: 'Australian Dollar vs US Dollar',
                tradingHours: '24/5', volatility: 'medium'
            },
            { 
                id: 18, symbol: 'AUDUSD_otc', name: 'AUD/USD OTC', 
                category: 'major', isOTC: true, 
                description: 'Australian Dollar vs US Dollar OTC',
                tradingHours: '24/7', volatility: 'medium'
            },
            { 
                id: 19, symbol: 'CADCHF', name: 'CAD/CHF', 
                category: 'minor', isOTC: false, 
                description: 'Canadian Dollar vs Swiss Franc',
                tradingHours: '24/5', volatility: 'medium'
            },
            { 
                id: 20, symbol: 'CADCHF_otc', name: 'CAD/CHF OTC', 
                category: 'minor', isOTC: true, 
                description: 'Canadian Dollar vs Swiss Franc OTC',
                tradingHours: '24/7', volatility: 'medium'
            },
            { 
                id: 21, symbol: 'EURJPY', name: 'EUR/JPY', 
                category: 'major', isOTC: false, 
                description: 'Euro vs Japanese Yen',
                tradingHours: '24/5', volatility: 'medium'
            },
            { 
                id: 22, symbol: 'EURJPY_otc', name: 'EUR/JPY OTC', 
                category: 'major', isOTC: true, 
                description: 'Euro vs Japanese Yen OTC',
                tradingHours: '24/7', volatility: 'medium'
            },
            { 
                id: 23, symbol: 'AUDCHF', name: 'AUD/CHF', 
                category: 'minor', isOTC: false, 
                description: 'Australian Dollar vs Swiss Franc',
                tradingHours: '24/5', volatility: 'medium'
            },
            { 
                id: 24, symbol: 'GBPCHF', name: 'GBP/CHF', 
                category: 'minor', isOTC: false, 
                description: 'British Pound vs Swiss Franc',
                tradingHours: '24/5', volatility: 'high'
            },
            { 
                id: 25, symbol: 'AUDJPY', name: 'AUD/JPY', 
                category: 'minor', isOTC: false, 
                description: 'Australian Dollar vs Japanese Yen',
                tradingHours: '24/5', volatility: 'high'
            },
            { 
                id: 26, symbol: 'AUDJPY_otc', name: 'AUD/JPY OTC', 
                category: 'minor', isOTC: true, 
                description: 'Australian Dollar vs Japanese Yen OTC',
                tradingHours: '24/7', volatility: 'high'
            },
            { 
                id: 27, symbol: 'GBPJPY', name: 'GBP/JPY', 
                category: 'minor', isOTC: false, 
                description: 'British Pound vs Japanese Yen',
                tradingHours: '24/5', volatility: 'high'
            },
            { 
                id: 28, symbol: 'GBPJPY_otc', name: 'GBP/JPY OTC', 
                category: 'minor', isOTC: true, 
                description: 'British Pound vs Japanese Yen OTC',
                tradingHours: '24/7', volatility: 'high'
            },
            { 
                id: 29, symbol: 'GBPAUD', name: 'GBP/AUD', 
                category: 'minor', isOTC: false, 
                description: 'British Pound vs Australian Dollar',
                tradingHours: '24/5', volatility: 'high'
            },
            { 
                id: 30, symbol: 'GBPAUD_otc', name: 'GBP/AUD OTC', 
                category: 'minor', isOTC: true, 
                description: 'British Pound vs Australian Dollar OTC',
                tradingHours: '24/7', volatility: 'high'
            },
            { 
                id: 31, symbol: 'GBPCAD', name: 'GBP/CAD', 
                category: 'minor', isOTC: false, 
                description: 'British Pound vs Canadian Dollar',
                tradingHours: '24/5', volatility: 'medium'
            },
            { 
                id: 32, symbol: 'CADJPY', name: 'CAD/JPY', 
                category: 'minor', isOTC: false, 
                description: 'Canadian Dollar vs Japanese Yen',
                tradingHours: '24/5', volatility: 'high'
            },
            { 
                id: 33, symbol: 'CADJPY_otc', name: 'CAD/JPY OTC', 
                category: 'minor', isOTC: true, 
                description: 'Canadian Dollar vs Japanese Yen OTC',
                tradingHours: '24/7', volatility: 'high'
            },
            { 
                id: 34, symbol: 'EURCHF', name: 'EUR/CHF', 
                category: 'major', isOTC: false, 
                description: 'Euro vs Swiss Franc',
                tradingHours: '24/5', volatility: 'low'
            },
            { 
                id: 35, symbol: 'EURCHF_otc', name: 'EUR/CHF OTC', 
                category: 'major', isOTC: true, 
                description: 'Euro vs Swiss Franc OTC',
                tradingHours: '24/7', volatility: 'low'
            },
            { 
                id: 36, symbol: 'EURUSD', name: 'EUR/USD', 
                category: 'major', isOTC: false, 
                description: 'Euro vs US Dollar',
                tradingHours: '24/5', volatility: 'medium'
            },
            {
                id: 37, symbol: 'EURUSD_otc', name: 'EUR/USD OTC',
                category: 'major', isOTC: true,
                description: 'Euro vs US Dollar OTC',
                tradingHours: '24/7', volatility: 'medium'
            },

            // الأزواج الغريبة والناشئة (33 زوج)
            {
                id: 38, symbol: 'USDPHP_otc', name: 'USD/PHP OTC',
                category: 'exotic', isOTC: true,
                description: 'US Dollar vs Philippine Peso OTC',
                tradingHours: '24/7', volatility: 'high'
            },
            {
                id: 39, symbol: 'USDSGD_otc', name: 'USD/SGD OTC',
                category: 'exotic', isOTC: true,
                description: 'US Dollar vs Singapore Dollar OTC',
                tradingHours: '24/7', volatility: 'medium'
            },
            {
                id: 40, symbol: 'USDVND_otc', name: 'USD/VND OTC',
                category: 'exotic', isOTC: true,
                description: 'US Dollar vs Vietnamese Dong OTC',
                tradingHours: '24/7', volatility: 'high'
            },
            {
                id: 41, symbol: 'USDMYR_otc', name: 'USD/MYR OTC',
                category: 'exotic', isOTC: true,
                description: 'US Dollar vs Malaysian Ringgit OTC',
                tradingHours: '24/7', volatility: 'medium'
            },
            {
                id: 42, symbol: 'NGNUSD_otc', name: 'NGN/USD OTC',
                category: 'exotic', isOTC: true,
                description: 'Nigerian Naira vs US Dollar OTC',
                tradingHours: '24/7', volatility: 'very_high'
            },
            {
                id: 43, symbol: 'USDRUB_otc', name: 'USD/RUB OTC',
                category: 'exotic', isOTC: true,
                description: 'US Dollar vs Russian Ruble OTC',
                tradingHours: '24/7', volatility: 'very_high'
            },
            {
                id: 44, symbol: 'TNDUSD_otc', name: 'TND/USD OTC',
                category: 'exotic', isOTC: true,
                description: 'Tunisian Dinar vs US Dollar OTC',
                tradingHours: '24/7', volatility: 'high'
            },
            {
                id: 45, symbol: 'NZDJPY_otc', name: 'NZD/JPY OTC',
                category: 'minor', isOTC: true,
                description: 'New Zealand Dollar vs Japanese Yen OTC',
                tradingHours: '24/7', volatility: 'high'
            },
            {
                id: 46, symbol: 'USDTHB_otc', name: 'USD/THB OTC',
                category: 'exotic', isOTC: true,
                description: 'US Dollar vs Thai Baht OTC',
                tradingHours: '24/7', volatility: 'medium'
            },
            {
                id: 47, symbol: 'LBPUSD_otc', name: 'LBP/USD OTC',
                category: 'exotic', isOTC: true,
                description: 'Lebanese Pound vs US Dollar OTC',
                tradingHours: '24/7', volatility: 'very_high'
            },
            {
                id: 48, symbol: 'USDBRL_otc', name: 'USD/BRL OTC',
                category: 'exotic', isOTC: true,
                description: 'US Dollar vs Brazilian Real OTC',
                tradingHours: '24/7', volatility: 'high'
            },
            {
                id: 49, symbol: 'USDPKR_otc', name: 'USD/PKR OTC',
                category: 'exotic', isOTC: true,
                description: 'US Dollar vs Pakistani Rupee OTC',
                tradingHours: '24/7', volatility: 'high'
            },
            {
                id: 50, symbol: 'EURNZD_otc', name: 'EUR/NZD OTC',
                category: 'minor', isOTC: true,
                description: 'Euro vs New Zealand Dollar OTC',
                tradingHours: '24/7', volatility: 'high'
            },
            {
                id: 51, symbol: 'USDDZD_otc', name: 'USD/DZD OTC',
                category: 'exotic', isOTC: true,
                description: 'US Dollar vs Algerian Dinar OTC',
                tradingHours: '24/7', volatility: 'high'
            },
            {
                id: 52, symbol: 'USDEGP_otc', name: 'USD/EGP OTC',
                category: 'exotic', isOTC: true,
                description: 'US Dollar vs Egyptian Pound OTC',
                tradingHours: '24/7', volatility: 'high'
            },
            {
                id: 53, symbol: 'NZDUSD_otc', name: 'NZD/USD OTC',
                category: 'major', isOTC: true,
                description: 'New Zealand Dollar vs US Dollar OTC',
                tradingHours: '24/7', volatility: 'medium'
            },
            {
                id: 54, symbol: 'AUDNZD_otc', name: 'AUD/NZD OTC',
                category: 'minor', isOTC: true,
                description: 'Australian Dollar vs New Zealand Dollar OTC',
                tradingHours: '24/7', volatility: 'medium'
            },
            {
                id: 55, symbol: 'YERUSD_otc', name: 'YER/USD OTC',
                category: 'exotic', isOTC: true,
                description: 'Yemeni Rial vs US Dollar OTC',
                tradingHours: '24/7', volatility: 'very_high'
            },
            {
                id: 56, symbol: 'EURHUF_otc', name: 'EUR/HUF OTC',
                category: 'exotic', isOTC: true,
                description: 'Euro vs Hungarian Forint OTC',
                tradingHours: '24/7', volatility: 'high'
            },
            {
                id: 57, symbol: 'USDMXN_otc', name: 'USD/MXN OTC',
                category: 'exotic', isOTC: true,
                description: 'US Dollar vs Mexican Peso OTC',
                tradingHours: '24/7', volatility: 'high'
            },
            {
                id: 58, symbol: 'IRRUSD_otc', name: 'IRR/USD OTC',
                category: 'exotic', isOTC: true,
                description: 'Iranian Rial vs US Dollar OTC',
                tradingHours: '24/7', volatility: 'very_high'
            },
            {
                id: 59, symbol: 'USDBDT_otc', name: 'USD/BDT OTC',
                category: 'exotic', isOTC: true,
                description: 'US Dollar vs Bangladeshi Taka OTC',
                tradingHours: '24/7', volatility: 'medium'
            },
            {
                id: 60, symbol: 'EURTRY_otc', name: 'EUR/TRY OTC',
                category: 'exotic', isOTC: true,
                description: 'Euro vs Turkish Lira OTC',
                tradingHours: '24/7', volatility: 'very_high'
            },
            {
                id: 61, symbol: 'USDIDR_otc', name: 'USD/IDR OTC',
                category: 'exotic', isOTC: true,
                description: 'US Dollar vs Indonesian Rupiah OTC',
                tradingHours: '24/7', volatility: 'medium'
            },
            {
                id: 62, symbol: 'USDINR_otc', name: 'USD/INR OTC',
                category: 'exotic', isOTC: true,
                description: 'US Dollar vs Indian Rupee OTC',
                tradingHours: '24/7', volatility: 'medium'
            },
            {
                id: 63, symbol: 'USDCLP_otc', name: 'USD/CLP OTC',
                category: 'exotic', isOTC: true,
                description: 'US Dollar vs Chilean Peso OTC',
                tradingHours: '24/7', volatility: 'high'
            },
            {
                id: 64, symbol: 'USDCNH_otc', name: 'USD/CNH OTC',
                category: 'exotic', isOTC: true,
                description: 'US Dollar vs Chinese Yuan Offshore OTC',
                tradingHours: '24/7', volatility: 'medium'
            },
            {
                id: 65, symbol: 'USDCOP_otc', name: 'USD/COP OTC',
                category: 'exotic', isOTC: true,
                description: 'US Dollar vs Colombian Peso OTC',
                tradingHours: '24/7', volatility: 'high'
            },
            {
                id: 66, symbol: 'ZARUSD_otc', name: 'ZAR/USD OTC',
                category: 'exotic', isOTC: true,
                description: 'South African Rand vs US Dollar OTC',
                tradingHours: '24/7', volatility: 'high'
            },
            {
                id: 67, symbol: 'USDARS_otc', name: 'USD/ARS OTC',
                category: 'exotic', isOTC: true,
                description: 'US Dollar vs Argentine Peso OTC',
                tradingHours: '24/7', volatility: 'very_high'
            },
            {
                id: 68, symbol: 'EURRUB_otc', name: 'EUR/RUB OTC',
                category: 'exotic', isOTC: true,
                description: 'Euro vs Russian Ruble OTC',
                tradingHours: '24/7', volatility: 'very_high'
            },
            {
                id: 69, symbol: 'CHFNOK_otc', name: 'CHF/NOK OTC',
                category: 'exotic', isOTC: true,
                description: 'Swiss Franc vs Norwegian Krone OTC',
                tradingHours: '24/7', volatility: 'medium'
            },
            {
                id: 70, symbol: 'SEKJPY_otc', name: 'SEK/JPY OTC',
                category: 'exotic', isOTC: true,
                description: 'Swedish Krona vs Japanese Yen OTC',
                tradingHours: '24/7', volatility: 'high'
            }
        ];
    }

    /**
     * الحصول على جميع الأزواج
     */
    getAllPairs() {
        return this.pairs;
    }

    /**
     * الحصول على الأزواج حسب الفئة
     */
    getPairsByCategory(category) {
        return this.pairs.filter(pair => pair.category === category);
    }

    /**
     * الحصول على أزواج OTC فقط
     */
    getOTCPairs() {
        return this.pairs.filter(pair => pair.isOTC === true);
    }

    /**
     * الحصول على الأزواج العادية فقط
     */
    getRegularPairs() {
        return this.pairs.filter(pair => pair.isOTC === false);
    }

    /**
     * البحث عن زوج بالرمز
     */
    findBySymbol(symbol) {
        return this.pairs.find(pair => pair.symbol === symbol);
    }

    /**
     * البحث عن زوج بالمعرف
     */
    findById(id) {
        return this.pairs.find(pair => pair.id === id);
    }

    /**
     * الحصول على عدد الأزواج
     */
    getCount() {
        return this.pairs.length;
    }

    /**
     * تصدير البيانات كـ JSON
     */
    exportToJSON() {
        return {
            timestamp: new Date().toISOString(),
            totalPairs: this.pairs.length,
            categories: {
                major: this.getPairsByCategory('major').length,
                minor: this.getPairsByCategory('minor').length,
                exotic: this.getPairsByCategory('exotic').length
            },
            otcPairs: this.getOTCPairs().length,
            regularPairs: this.getRegularPairs().length,
            pairs: this.pairs
        };
    }
}

module.exports = TargetPairs;
