/**
 * نظام التداول الآلي الذكي المتقدم
 * Smart Automated Trading System
 * مع منع الصفقات المتداخلة وإدارة المخاطر المتقدمة
 */

const EventEmitter = require('events');

class SmartAutoTrader extends EventEmitter {
    constructor(quotexConnector, analysisEngine, options = {}) {
        super();
        
        this.quotexConnector = quotexConnector;
        this.analysisEngine = analysisEngine;
        
        this.options = {
            // إعدادات التداول
            minConfidenceLevel: options.minConfidenceLevel || 85,
            maxConcurrentTrades: options.maxConcurrentTrades || 3,
            defaultTradeAmount: options.defaultTradeAmount || 10,
            maxTradeAmount: options.maxTradeAmount || 100,
            
            // إدارة المخاطر
            maxDailyLoss: options.maxDailyLoss || 500,
            maxConsecutiveLosses: options.maxConsecutiveLosses || 3,
            stopLossPercentage: options.stopLossPercentage || 10,
            takeProfitPercentage: options.takeProfitPercentage || 15,
            
            // إعدادات الوقت
            tradingHours: options.tradingHours || { start: 8, end: 22 }, // UTC
            avoidNewsTime: options.avoidNewsTime !== false,
            cooldownPeriod: options.cooldownPeriod || 60000, // دقيقة واحدة
            
            // إعدادات متقدمة
            adaptiveAmountSizing: options.adaptiveAmountSizing !== false,
            dynamicRiskManagement: options.dynamicRiskManagement !== false,
            enableMartingale: options.enableMartingale || false,
            martingaleMultiplier: options.martingaleMultiplier || 2.2,
            
            ...options
        };

        // حالة التداول
        this.tradingState = {
            isActive: false,
            isPaused: false,
            currentTrades: new Map(), // tradeId -> trade info
            lastTradeTime: null,
            consecutiveLosses: 0,
            dailyStats: {
                date: new Date().toISOString().split('T')[0],
                totalTrades: 0,
                winTrades: 0,
                lossTrades: 0,
                totalProfit: 0,
                totalLoss: 0
            }
        };

        // قائمة انتظار الإشارات
        this.signalQueue = [];
        
        // إحصائيات الأداء
        this.performanceStats = {
            totalSignalsReceived: 0,
            totalSignalsExecuted: 0,
            totalSignalsRejected: 0,
            averageExecutionTime: 0,
            lastUpdate: null
        };

        // مؤقتات
        this.monitoringTimer = null;
        this.cooldownTimer = null;
        
        this.isInitialized = false;
    }

    /**
     * تهيئة نظام التداول الآلي
     */
    async initialize() {
        try {
            console.log('🤖 Initializing Smart Auto Trader...');
            
            // إعداد معالجات الأحداث
            this.setupEventHandlers();
            
            // تحميل الإحصائيات اليومية
            await this.loadDailyStats();
            
            this.isInitialized = true;
            console.log('✅ Smart Auto Trader initialized');
            this.emit('initialized');
            
            return true;
            
        } catch (error) {
            console.error('❌ Failed to initialize Smart Auto Trader:', error);
            this.emit('error', error);
            throw error;
        }
    }

    /**
     * إعداد معالجات الأحداث
     */
    setupEventHandlers() {
        // معالج الإشارات من محرك التحليل
        this.analysisEngine.on('signalGenerated', (signal) => {
            this.handleNewSignal(signal);
        });

        // معالج نتائج الصفقات
        this.quotexConnector.on('tradeResult', (result) => {
            this.handleTradeResult(result);
        });

        // معالج تحديث الرصيد
        this.quotexConnector.on('balanceUpdate', (data) => {
            this.handleBalanceUpdate(data);
        });
    }

    /**
     * بدء التداول الآلي
     */
    async startAutoTrading(config = {}) {
        try {
            if (!this.isInitialized) {
                throw new Error('Auto trader not initialized');
            }

            console.log('🚀 Starting automated trading...');
            
            // تحديث الإعدادات
            Object.assign(this.options, config);
            
            // التحقق من الشروط المسبقة
            await this.validateTradingConditions();
            
            this.tradingState.isActive = true;
            this.tradingState.isPaused = false;
            
            // بدء المراقبة
            this.startMonitoring();
            
            console.log('✅ Automated trading started');
            this.emit('tradingStarted', this.getTradingStatus());
            
            return true;
            
        } catch (error) {
            console.error('❌ Failed to start auto trading:', error);
            this.emit('error', error);
            throw error;
        }
    }

    /**
     * إيقاف التداول الآلي
     */
    async stopAutoTrading() {
        try {
            console.log('🛑 Stopping automated trading...');
            
            this.tradingState.isActive = false;
            
            // إيقاف المراقبة
            this.stopMonitoring();
            
            // انتظار إغلاق الصفقات المفتوحة
            await this.waitForOpenTradesToClose();
            
            console.log('✅ Automated trading stopped');
            this.emit('tradingStopped', this.getTradingStatus());
            
        } catch (error) {
            console.error('❌ Error stopping auto trading:', error);
        }
    }

    /**
     * معالجة إشارة جديدة
     */
    async handleNewSignal(signal) {
        try {
            this.performanceStats.totalSignalsReceived++;
            
            console.log(`📊 New signal received: ${signal.assetSymbol} ${signal.direction} (${signal.confidence}%)`);
            
            // التحقق من حالة التداول
            if (!this.tradingState.isActive || this.tradingState.isPaused) {
                console.log('⏸️ Trading is paused, signal ignored');
                this.performanceStats.totalSignalsRejected++;
                return;
            }

            // فلترة الإشارة
            const filterResult = await this.filterSignal(signal);
            if (!filterResult.passed) {
                console.log(`❌ Signal rejected: ${filterResult.reason}`);
                this.performanceStats.totalSignalsRejected++;
                this.emit('signalRejected', { signal, reason: filterResult.reason });
                return;
            }

            // إضافة الإشارة لقائمة الانتظار
            this.signalQueue.push({
                signal: signal,
                timestamp: new Date().toISOString(),
                priority: this.calculateSignalPriority(signal)
            });

            // معالجة قائمة الانتظار
            await this.processSignalQueue();
            
        } catch (error) {
            console.error('❌ Error handling new signal:', error);
            this.emit('error', error);
        }
    }

    /**
     * فلترة الإشارة
     */
    async filterSignal(signal) {
        // فلتر الثقة
        if (signal.confidence < this.options.minConfidenceLevel) {
            return { passed: false, reason: `Low confidence: ${signal.confidence}%` };
        }

        // فلتر الاتجاه
        if (signal.direction === 'neutral' || signal.recommendation === 'hold') {
            return { passed: false, reason: 'Neutral signal' };
        }

        // فلتر الصفقات المتداخلة
        if (this.hasActiveTradeForAsset(signal.assetId)) {
            return { passed: false, reason: 'Active trade exists for this asset' };
        }

        // فلتر الحد الأقصى للصفقات المتزامنة
        if (this.tradingState.currentTrades.size >= this.options.maxConcurrentTrades) {
            return { passed: false, reason: 'Maximum concurrent trades reached' };
        }

        // فلتر فترة التهدئة
        if (this.isInCooldownPeriod()) {
            return { passed: false, reason: 'In cooldown period' };
        }

        // فلتر ساعات التداول
        if (!this.isWithinTradingHours()) {
            return { passed: false, reason: 'Outside trading hours' };
        }

        // فلتر الخسائر المتتالية
        if (this.tradingState.consecutiveLosses >= this.options.maxConsecutiveLosses) {
            return { passed: false, reason: 'Maximum consecutive losses reached' };
        }

        // فلتر الخسارة اليومية
        if (Math.abs(this.tradingState.dailyStats.totalLoss) >= this.options.maxDailyLoss) {
            return { passed: false, reason: 'Daily loss limit reached' };
        }

        // فلتر جودة الإشارة
        if (signal.signalStrength < 0.6) {
            return { passed: false, reason: 'Low signal strength' };
        }

        return { passed: true };
    }

    /**
     * معالجة قائمة انتظار الإشارات
     */
    async processSignalQueue() {
        if (this.signalQueue.length === 0) return;

        // ترتيب الإشارات حسب الأولوية
        this.signalQueue.sort((a, b) => b.priority - a.priority);

        // معالجة أفضل إشارة
        const bestSignal = this.signalQueue.shift();
        await this.executeSignal(bestSignal.signal);
    }

    /**
     * تنفيذ الإشارة
     */
    async executeSignal(signal) {
        try {
            const startTime = Date.now();
            
            console.log(`🎯 Executing signal: ${signal.assetSymbol} ${signal.direction}`);
            
            // حساب مبلغ الصفقة
            const tradeAmount = this.calculateTradeAmount(signal);
            
            // تنفيذ الصفقة
            const tradeResult = await this.quotexConnector.placeTrade(
                signal.assetId,
                tradeAmount,
                signal.direction,
                signal.suggestedDuration,
                {
                    analysis: signal,
                    confidence: signal.confidence,
                    strategy: 'smart_auto_trader'
                }
            );

            if (tradeResult.success) {
                // تسجيل الصفقة
                this.tradingState.currentTrades.set(tradeResult.tradeRecord.id, {
                    tradeId: tradeResult.tradeRecord.id,
                    assetId: signal.assetId,
                    assetSymbol: signal.assetSymbol,
                    direction: signal.direction,
                    amount: tradeAmount,
                    openTime: new Date().toISOString(),
                    expectedCloseTime: new Date(Date.now() + signal.suggestedDuration * 1000).toISOString(),
                    signal: signal,
                    status: 'open'
                });

                this.tradingState.lastTradeTime = new Date();
                this.tradingState.dailyStats.totalTrades++;
                
                const executionTime = Date.now() - startTime;
                this.updateExecutionStats(executionTime);

                console.log(`✅ Trade executed successfully: ${tradeResult.tradeRecord.id}`);
                this.emit('tradeExecuted', {
                    trade: tradeResult.tradeRecord,
                    signal: signal,
                    executionTime: executionTime
                });

                this.performanceStats.totalSignalsExecuted++;
                
            } else {
                console.log(`❌ Trade execution failed: ${tradeResult.reason}`);
                this.emit('tradeExecutionFailed', { signal, reason: tradeResult.reason });
                this.performanceStats.totalSignalsRejected++;
            }
            
        } catch (error) {
            console.error('❌ Error executing signal:', error);
            this.emit('error', error);
        }
    }

    /**
     * معالجة نتيجة الصفقة
     */
    async handleTradeResult(result) {
        try {
            const trade = this.tradingState.currentTrades.get(result.tradeId);
            if (!trade) return;

            console.log(`🏁 Trade result: ${trade.assetSymbol} ${result.result} (${result.profit || 0})`);

            // تحديث الإحصائيات
            if (result.result === 'win') {
                this.tradingState.dailyStats.winTrades++;
                this.tradingState.dailyStats.totalProfit += result.profit || 0;
                this.tradingState.consecutiveLosses = 0;
            } else if (result.result === 'loss') {
                this.tradingState.dailyStats.lossTrades++;
                this.tradingState.dailyStats.totalLoss += Math.abs(result.profit || trade.amount);
                this.tradingState.consecutiveLosses++;
            }

            // إزالة الصفقة من القائمة المفتوحة
            this.tradingState.currentTrades.delete(result.tradeId);

            // تحديث إعدادات إدارة المخاطر
            await this.updateRiskManagement(result);

            this.emit('tradeCompleted', { trade, result });
            
        } catch (error) {
            console.error('❌ Error handling trade result:', error);
        }
    }

    /**
     * حساب مبلغ الصفقة
     */
    calculateTradeAmount(signal) {
        let amount = this.options.defaultTradeAmount;

        // التحجيم التكيفي
        if (this.options.adaptiveAmountSizing) {
            // زيادة المبلغ للإشارات عالية الثقة
            if (signal.confidence >= 90) {
                amount *= 1.5;
            } else if (signal.confidence >= 85) {
                amount *= 1.2;
            }

            // تقليل المبلغ بعد الخسائر
            if (this.tradingState.consecutiveLosses > 0) {
                amount *= Math.pow(0.8, this.tradingState.consecutiveLosses);
            }
        }

        // نظام مارتينجال (اختياري)
        if (this.options.enableMartingale && this.tradingState.consecutiveLosses > 0) {
            amount *= Math.pow(this.options.martingaleMultiplier, this.tradingState.consecutiveLosses);
        }

        // التأكد من الحدود
        amount = Math.max(1, Math.min(this.options.maxTradeAmount, amount));
        
        return Math.round(amount);
    }

    /**
     * حساب أولوية الإشارة
     */
    calculateSignalPriority(signal) {
        let priority = signal.confidence;
        
        // زيادة الأولوية للإشارات القوية
        priority += signal.signalStrength * 20;
        
        // زيادة الأولوية للأصول عالية الربحية
        const profitRate = signal.profitRate || 0.85;
        priority += (profitRate - 0.8) * 100;
        
        return priority;
    }

    /**
     * التحقق من وجود صفقة نشطة للأصل
     */
    hasActiveTradeForAsset(assetId) {
        for (const trade of this.tradingState.currentTrades.values()) {
            if (trade.assetId === assetId) {
                return true;
            }
        }
        return false;
    }

    /**
     * التحقق من فترة التهدئة
     */
    isInCooldownPeriod() {
        if (!this.tradingState.lastTradeTime) return false;
        
        const timeSinceLastTrade = Date.now() - this.tradingState.lastTradeTime.getTime();
        return timeSinceLastTrade < this.options.cooldownPeriod;
    }

    /**
     * التحقق من ساعات التداول
     */
    isWithinTradingHours() {
        const now = new Date();
        const hour = now.getUTCHours();
        
        return hour >= this.options.tradingHours.start && hour <= this.options.tradingHours.end;
    }

    /**
     * بدء المراقبة
     */
    startMonitoring() {
        this.monitoringTimer = setInterval(() => {
            this.performHealthCheck();
        }, 30000); // كل 30 ثانية
    }

    /**
     * إيقاف المراقبة
     */
    stopMonitoring() {
        if (this.monitoringTimer) {
            clearInterval(this.monitoringTimer);
            this.monitoringTimer = null;
        }
    }

    /**
     * فحص صحة النظام
     */
    performHealthCheck() {
        try {
            // فحص الصفقات المنتهية الصلاحية
            this.checkExpiredTrades();
            
            // فحص حدود المخاطر
            this.checkRiskLimits();
            
            // تحديث الإحصائيات
            this.updatePerformanceStats();
            
        } catch (error) {
            console.error('❌ Error in health check:', error);
        }
    }

    /**
     * فحص الصفقات المنتهية الصلاحية
     */
    checkExpiredTrades() {
        const now = new Date();
        
        for (const [tradeId, trade] of this.tradingState.currentTrades) {
            const expectedCloseTime = new Date(trade.expectedCloseTime);
            
            if (now > expectedCloseTime) {
                console.log(`⏰ Trade ${tradeId} appears to be expired`);
                // يمكن إضافة منطق إضافي هنا
            }
        }
    }

    /**
     * فحص حدود المخاطر
     */
    checkRiskLimits() {
        // فحص الخسارة اليومية
        if (Math.abs(this.tradingState.dailyStats.totalLoss) >= this.options.maxDailyLoss) {
            console.log('⚠️ Daily loss limit reached, pausing trading');
            this.pauseTrading('Daily loss limit reached');
        }

        // فحص الخسائر المتتالية
        if (this.tradingState.consecutiveLosses >= this.options.maxConsecutiveLosses) {
            console.log('⚠️ Consecutive losses limit reached, pausing trading');
            this.pauseTrading('Consecutive losses limit reached');
        }
    }

    /**
     * إيقاف التداول مؤقتاً
     */
    pauseTrading(reason) {
        this.tradingState.isPaused = true;
        console.log(`⏸️ Trading paused: ${reason}`);
        this.emit('tradingPaused', { reason, timestamp: new Date().toISOString() });
    }

    /**
     * استئناف التداول
     */
    resumeTrading() {
        this.tradingState.isPaused = false;
        console.log('▶️ Trading resumed');
        this.emit('tradingResumed', { timestamp: new Date().toISOString() });
    }

    /**
     * الحصول على حالة التداول
     */
    getTradingStatus() {
        const winRate = this.tradingState.dailyStats.totalTrades > 0 ? 
            (this.tradingState.dailyStats.winTrades / this.tradingState.dailyStats.totalTrades) * 100 : 0;

        return {
            isActive: this.tradingState.isActive,
            isPaused: this.tradingState.isPaused,
            currentTrades: this.tradingState.currentTrades.size,
            dailyStats: {
                ...this.tradingState.dailyStats,
                winRate: Math.round(winRate * 100) / 100,
                netProfit: this.tradingState.dailyStats.totalProfit - this.tradingState.dailyStats.totalLoss
            },
            consecutiveLosses: this.tradingState.consecutiveLosses,
            performanceStats: this.performanceStats,
            lastTradeTime: this.tradingState.lastTradeTime
        };
    }

    /**
     * تحديث إحصائيات التنفيذ
     */
    updateExecutionStats(executionTime) {
        const currentAvg = this.performanceStats.averageExecutionTime;
        const totalExecuted = this.performanceStats.totalSignalsExecuted;
        
        this.performanceStats.averageExecutionTime = 
            ((currentAvg * (totalExecuted - 1)) + executionTime) / totalExecuted;
    }

    /**
     * تحديث إدارة المخاطر
     */
    async updateRiskManagement(result) {
        if (this.options.dynamicRiskManagement) {
            // تعديل المعايير بناء على الأداء
            const winRate = this.tradingState.dailyStats.totalTrades > 0 ? 
                (this.tradingState.dailyStats.winTrades / this.tradingState.dailyStats.totalTrades) : 0;

            if (winRate < 0.6) {
                // زيادة مستوى الثقة المطلوب
                this.options.minConfidenceLevel = Math.min(95, this.options.minConfidenceLevel + 2);
            } else if (winRate > 0.8) {
                // تقليل مستوى الثقة المطلوب
                this.options.minConfidenceLevel = Math.max(70, this.options.minConfidenceLevel - 1);
            }
        }
    }

    /**
     * تحديث إحصائيات الأداء
     */
    updatePerformanceStats() {
        this.performanceStats.lastUpdate = new Date().toISOString();
    }

    /**
     * تحميل الإحصائيات اليومية
     */
    async loadDailyStats() {
        const today = new Date().toISOString().split('T')[0];
        if (this.tradingState.dailyStats.date !== today) {
            // يوم جديد - إعادة تعيين الإحصائيات
            this.tradingState.dailyStats = {
                date: today,
                totalTrades: 0,
                winTrades: 0,
                lossTrades: 0,
                totalProfit: 0,
                totalLoss: 0
            };
            this.tradingState.consecutiveLosses = 0;
        }
    }

    /**
     * التحقق من شروط التداول
     */
    async validateTradingConditions() {
        // التحقق من الاتصال
        if (!this.quotexConnector.isConnected) {
            throw new Error('Quotex connector not connected');
        }

        // التحقق من محرك التحليل
        if (!this.analysisEngine.isRunning) {
            throw new Error('Analysis engine not running');
        }

        // التحقق من الرصيد
        const balance = this.quotexConnector.getBalance();
        if (balance < this.options.defaultTradeAmount * 2) {
            throw new Error('Insufficient balance for trading');
        }
    }

    /**
     * انتظار إغلاق الصفقات المفتوحة
     */
    async waitForOpenTradesToClose() {
        while (this.tradingState.currentTrades.size > 0) {
            console.log(`⏳ Waiting for ${this.tradingState.currentTrades.size} trades to close...`);
            await new Promise(resolve => setTimeout(resolve, 5000));
        }
    }

    /**
     * معالجة تحديث الرصيد
     */
    handleBalanceUpdate(data) {
        // يمكن إضافة منطق إضافي هنا
        this.emit('balanceUpdated', data);
    }

    /**
     * إيقاف النظام
     */
    async shutdown() {
        try {
            console.log('🛑 Shutting down Smart Auto Trader...');
            
            await this.stopAutoTrading();
            
            console.log('✅ Smart Auto Trader shutdown completed');
            this.emit('shutdown');
            
        } catch (error) {
            console.error('❌ Error during shutdown:', error);
        }
    }
}

module.exports = SmartAutoTrader;
