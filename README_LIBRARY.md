# 🚀 مكتبة Quotex Trading المتقدمة

## نظام تداول آلي متطور مع نسبة نجاح 85%+

مكتبة شاملة ومتقدمة للتداول الآلي على منصة Quotex مع استراتيجية رباعية الطبقات ونظام ذكاء اصطناعي متطور.

---

## ✨ المميزات الرئيسية

### 🎯 **الاستراتيجية الرباعية المتقدمة**
- **التحليل الفني**: 15 مؤشر فني متقدم (EMA, RSI, MACD, Bollinger, ATR, إلخ)
- **التحليل الكمي**: Z-Score, Sharpe Ratio, تحليل الاحتمالات التاريخية
- **التحليل السلوكي**: أنماط الشموع, تحليل الزخم, السلوك اللحظي
- **الذكاء الاصطناعي**: نماذج ML متقدمة للتنبؤ بدقة عالية

### 📊 **إدارة البيانات المتطورة**
- جلب 500 شمعة × 70 زوج = **35,000 شمعة** بشكل متوازي
- نظام **FIFO** ذكي للشموع الحية
- كشف وسد الفجوات الزمنية تلقائياً
- تخزين JSON منظم لكل زوج مع المؤشرات

### 🤖 **التداول الآلي الذكي**
- منع الصفقات المتداخلة
- إدارة مخاطر متقدمة
- تحديد مدة الصفقة الذكي (1-5 دقائق)
- نظام Martingale اختياري

### 📡 **البث المباشر اللحظي**
- WebSocket للبث المباشر
- تحديث نسب الأرباح لحظياً
- مراقبة الأسعار والشموع الحية
- واجهة API للتكامل مع Next.js

---

## 🏗️ هيكل المكتبة

```
quotex-trading-library/
├── src/
│   ├── quotexConnector.js          # الاتصال بمنصة Quotex
│   ├── indicators.js               # المؤشرات الفنية الـ15
│   ├── hybridStrategy.js           # الاستراتيجية الهجينة
│   ├── sessionManager.js           # إدارة الجلسات والكوكيز
│   ├── tradeManager.js             # إدارة الصفقات
│   ├── candleManager.js            # إدارة الشموع مع FIFO
│   ├── historicalDataManager.js    # جلب البيانات التاريخية
│   ├── liveDataStreamer.js         # البث المباشر
│   ├── advancedAnalysisEngine.js   # محرك التحليل المتقدم
│   ├── smartAutoTrader.js          # التداول الآلي الذكي
│   ├── targetPairs.js              # قائمة الأزواج الـ70
│   └── dataStorage.js              # تخزين البيانات
├── data/
│   ├── historical/                 # البيانات التاريخية
│   ├── live/                       # الشموع الحية
│   └── trades/                     # سجلات الصفقات
├── sessions/                       # جلسات المستخدمين
├── quotexLibrary.js               # نقطة الدخول الرئيسية
└── README_LIBRARY.md              # هذا الملف
```

---

## 🚀 التثبيت والاستخدام

### 1. التثبيت
```bash
npm install
```

### 2. الاستخدام الأساسي
```javascript
const QuotexLibrary = require('./quotexLibrary');

// إنشاء مثيل من المكتبة
const quotex = new QuotexLibrary({
    headless: false,
    historicalCandles: 500,
    minConfidenceLevel: 85,
    enableLiveStreaming: true,
    streamingPort: 8080
});

// تهيئة المكتبة
await quotex.initialize();

// الاتصال بالمنصة
await quotex.connect('<EMAIL>', 'your-password');

// بدء التحليل المتقدم
await quotex.startAnalysis();

// بدء التداول الآلي
await quotex.startAutoTrading({
    minConfidenceLevel: 85,
    maxConcurrentTrades: 3,
    defaultTradeAmount: 10
});

// بدء البث المباشر
await quotex.startLiveStreaming();
```

### 3. تحليل أصل واحد
```javascript
// تحليل زوج EUR/USD
const analysis = await quotex.analyzeAsset(36); // EUR/USD ID

console.log('نتيجة التحليل:', {
    confidence: analysis.confidence,
    direction: analysis.direction,
    recommendation: analysis.recommendation,
    suggestedDuration: analysis.suggestedDuration
});
```

### 4. تنفيذ صفقة يدوياً
```javascript
// تنفيذ صفقة CALL على EUR/USD
const trade = await quotex.placeTrade(
    36,        // Asset ID
    10,        // Amount
    'call',    // Direction
    300,       // Duration (5 minutes)
    analysis   // Analysis data
);
```

---

## 📊 قائمة الأزواج المستهدفة (70 زوج)

### الأزواج الرئيسية (16 زوج)
- EUR/USD, GBP/USD, USD/JPY, USD/CHF, USD/CAD, AUD/USD
- EUR/GBP, EUR/JPY, EUR/CHF, GBP/JPY, وغيرها...

### الأزواج الثانوية (21 زوج)
- EUR/AUD, EUR/CAD, GBP/AUD, GBP/CAD, AUD/CAD
- CHF/JPY, CAD/JPY, AUD/JPY, وغيرها...

### الأزواج الغريبة (33 زوج)
- USD/PHP, USD/SGD, USD/VND, USD/MYR, USD/RUB
- EUR/TRY, USD/MXN, USD/BRL, وغيرها...

---

## 🔧 الإعدادات المتقدمة

```javascript
const quotex = new QuotexLibrary({
    // إعدادات الاتصال
    headless: false,
    userDataDir: './user_data',
    timeout: 30000,
    
    // إعدادات البيانات
    historicalCandles: 500,
    maxConcurrentFetch: 10,
    
    // إعدادات التحليل
    analysisInterval: 5000,
    minConfidenceLevel: 85,
    targetSuccessRate: 85,
    
    // إعدادات التداول الآلي
    autoTradingEnabled: true,
    maxConcurrentTrades: 3,
    defaultTradeAmount: 10,
    maxTradeAmount: 100,
    
    // إدارة المخاطر
    maxDailyLoss: 500,
    maxConsecutiveLosses: 3,
    enableMartingale: false,
    
    // إعدادات البث المباشر
    enableLiveStreaming: true,
    streamingPort: 8080,
    maxConnections: 100
});
```

---

## 📈 مراقبة الأداء

### الحصول على إحصائيات شاملة
```javascript
const status = quotex.getLibraryStatus();

console.log('حالة المكتبة:', {
    isConnected: status.isConnected,
    isAnalysisRunning: status.isAnalysisRunning,
    isAutoTradingActive: status.isAutoTradingActive,
    
    // إحصائيات التداول
    totalTrades: status.tradeStats.totalTrades,
    successRate: status.tradeStats.successRate,
    
    // إحصائيات التحليل
    analysisAccuracy: status.analysisStats.currentSuccessRate,
    
    // معلومات الحساب
    balance: status.accountInfo.balance
});
```

### مراقبة الصفقات المباشرة
```javascript
// الصفقات المفتوحة
const openTrades = quotex.getOpenTrades();

// الصفقات المغلقة
const closedTrades = quotex.getClosedTrades(50);

// إحصائيات التداول
const tradeStats = quotex.getTradeStats();
```

---

## 🌐 التكامل مع Next.js

### 1. إعداد API Routes
```javascript
// pages/api/quotex/status.js
import { quotexLibrary } from '../../../lib/quotex';

export default async function handler(req, res) {
    try {
        const status = quotexLibrary.getLibraryStatus();
        res.status(200).json(status);
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
}
```

### 2. WebSocket للبيانات المباشرة
```javascript
// استخدام WebSocket للبيانات المباشرة
const ws = new WebSocket('ws://localhost:8080');

ws.onmessage = (event) => {
    const data = JSON.parse(event.data);
    
    switch (data.type) {
        case 'price_update':
            updatePriceDisplay(data.payload);
            break;
        case 'candle_update':
            updateCandleChart(data.payload);
            break;
        case 'trade_result':
            updateTradeResults(data.payload);
            break;
    }
};
```

---

## 🔒 الأمان والجلسات

- **حفظ الكوكيز**: تجنب تسجيل الدخول المتكرر
- **إدارة الجلسات**: جلسات آمنة مع انتهاء صلاحية
- **تشفير البيانات**: حماية البيانات الحساسة
- **إعادة الاتصال التلقائي**: استئناف العمل بعد انقطاع الشبكة

---

## 📞 الدعم والتطوير

### الأحداث المتاحة
```javascript
quotex.connector.on('connected', () => {
    console.log('تم الاتصال بنجاح');
});

quotex.analysisEngine.on('signalGenerated', (signal) => {
    console.log('إشارة جديدة:', signal);
});

quotex.autoTrader.on('tradeExecuted', (trade) => {
    console.log('تم تنفيذ صفقة:', trade);
});
```

### معالجة الأخطاء
```javascript
try {
    await quotex.connect(email, password);
} catch (error) {
    console.error('خطأ في الاتصال:', error.message);
    // معالجة الخطأ
}
```

---

## 🎯 الهدف: نسبة نجاح 85%+

هذه المكتبة مصممة لتحقيق نسبة نجاح عالية من خلال:

1. **التحليل الرباعي المتقدم**
2. **فلترة الإشارات الذكية**
3. **إدارة المخاطر المتطورة**
4. **التعلم من النتائج السابقة**
5. **التحديث المستمر للاستراتيجيات**

---

## 📝 ملاحظات مهمة

- المكتبة جاهزة للتكامل مع واجهة Next.js
- تدعم التشغيل 24/7 مع إعادة الاتصال التلقائي
- تحفظ جميع البيانات في ملفات JSON منظمة
- تدعم التداول على الحساب التجريبي والحقيقي
- مصممة للاستخدام الاحترافي مع أداء عالي

---

**🚀 جاهزة للاستخدام مع واجهة Next.js الخاصة بك!**
