/**
 * مدير البيانات التاريخية المتوازي المتقدم
 * Advanced Parallel Historical Data Manager
 * جلب 500 شمعة × 70 زوج = 35,000 شمعة بشكل متوازي مع كشف الفجوات الذكي
 */

const fs = require('fs').promises;
const path = require('path');
const EventEmitter = require('events');

class HistoricalDataManager extends EventEmitter {
    constructor(quotexConnector, options = {}) {
        super();
        
        this.quotexConnector = quotexConnector;
        this.options = {
            dataDir: options.dataDir || './data/historical',
            candlesPerAsset: options.candlesPerAsset || 500,
            timeframe: options.timeframe || 300, // 5 دقائق بالثواني
            maxConcurrent: options.maxConcurrent || 10, // عدد الطلبات المتوازية
            retryAttempts: options.retryAttempts || 3,
            retryDelay: options.retryDelay || 2000,
            gapDetectionEnabled: options.gapDetectionEnabled !== false,
            autoFillGaps: options.autoFillGaps !== false,
            ...options
        };

        // قائمة الأزواج المستهدفة
        this.targetPairs = [];
        
        // تتبع التقدم
        this.progress = {
            totalPairs: 0,
            completedPairs: 0,
            totalCandles: 0,
            fetchedCandles: 0,
            errors: 0,
            gaps: 0,
            startTime: null,
            endTime: null
        };

        // تخزين البيانات المؤقت
        this.dataCache = new Map();
        
        // قائمة انتظار المعالجة
        this.processingQueue = [];
        this.isProcessing = false;
        
        this.isInitialized = false;
    }

    /**
     * تهيئة مدير البيانات التاريخية
     */
    async initialize() {
        try {
            console.log('📊 Initializing Historical Data Manager...');
            
            // إنشاء مجلد البيانات
            await this.ensureDataDirectory();
            
            // تحميل قائمة الأزواج المستهدفة
            this.targetPairs = this.quotexConnector.getTargetPairs();
            this.progress.totalPairs = this.targetPairs.length;
            this.progress.totalCandles = this.targetPairs.length * this.options.candlesPerAsset;
            
            console.log(`🎯 Target: ${this.progress.totalPairs} pairs × ${this.options.candlesPerAsset} candles = ${this.progress.totalCandles} total candles`);
            
            this.isInitialized = true;
            this.emit('initialized');
            
            return true;
            
        } catch (error) {
            console.error('❌ Failed to initialize Historical Data Manager:', error);
            this.emit('error', error);
            throw error;
        }
    }

    /**
     * التأكد من وجود مجلد البيانات
     */
    async ensureDataDirectory() {
        try {
            await fs.access(this.options.dataDir);
        } catch {
            await fs.mkdir(this.options.dataDir, { recursive: true });
            console.log(`📁 Created data directory: ${this.options.dataDir}`);
        }
    }

    /**
     * جلب البيانات التاريخية لجميع الأزواج بشكل متوازي
     */
    async fetchAllHistoricalData() {
        try {
            console.log('🚀 Starting parallel historical data fetch...');
            this.progress.startTime = new Date();
            
            // تقسيم الأزواج إلى مجموعات للمعالجة المتوازية
            const chunks = this.chunkArray(this.targetPairs, this.options.maxConcurrent);
            
            for (const chunk of chunks) {
                // معالجة كل مجموعة بشكل متوازي
                const promises = chunk.map(pair => this.fetchPairHistoricalData(pair));
                await Promise.allSettled(promises);
                
                // تحديث التقدم
                this.emit('progressUpdate', this.getProgress());
                
                // فترة راحة قصيرة بين المجموعات
                await this.delay(1000);
            }
            
            this.progress.endTime = new Date();
            console.log('✅ Historical data fetch completed');
            this.emit('fetchCompleted', this.getProgress());
            
            return this.getProgress();
            
        } catch (error) {
            console.error('❌ Error fetching historical data:', error);
            this.emit('error', error);
            throw error;
        }
    }

    /**
     * جلب البيانات التاريخية لزوج واحد
     */
    async fetchPairHistoricalData(pair) {
        try {
            console.log(`📈 Fetching data for ${pair.symbol}...`);
            
            // التحقق من البيانات الموجودة
            const existingData = await this.loadExistingData(pair.symbol);
            
            // تحديد البيانات المطلوبة
            const requiredCandles = await this.determineRequiredCandles(pair, existingData);
            
            if (requiredCandles.length === 0) {
                console.log(`✅ ${pair.symbol}: Data is up to date`);
                this.progress.completedPairs++;
                return;
            }

            // جلب البيانات الجديدة
            const newCandles = await this.fetchCandlesWithRetry(pair, requiredCandles);
            
            // دمج البيانات الجديدة مع الموجودة
            const mergedData = this.mergeHistoricalData(existingData, newCandles);
            
            // حفظ البيانات
            await this.saveHistoricalData(pair.symbol, mergedData);
            
            this.progress.completedPairs++;
            this.progress.fetchedCandles += newCandles.length;
            
            console.log(`✅ ${pair.symbol}: Fetched ${newCandles.length} candles`);
            this.emit('pairCompleted', { pair, candlesCount: newCandles.length });
            
        } catch (error) {
            console.error(`❌ Error fetching data for ${pair.symbol}:`, error);
            this.progress.errors++;
            this.emit('pairError', { pair, error });
        }
    }

    /**
     * تحديد الشموع المطلوبة (كشف الفجوات)
     */
    async determineRequiredCandles(pair, existingData) {
        try {
            const now = new Date();
            const timeframeMs = this.options.timeframe * 1000;
            const totalDuration = this.options.candlesPerAsset * timeframeMs;
            const startTime = new Date(now.getTime() - totalDuration);
            
            if (!existingData || existingData.length === 0) {
                // لا توجد بيانات - جلب كل شيء
                return this.generateTimeRange(startTime, now, timeframeMs);
            }

            // كشف الفجوات في البيانات الموجودة
            const gaps = this.detectGaps(existingData, startTime, now, timeframeMs);
            
            if (gaps.length > 0) {
                console.log(`🔍 ${pair.symbol}: Found ${gaps.length} gaps`);
                this.progress.gaps += gaps.length;
            }

            return gaps;
            
        } catch (error) {
            console.error(`❌ Error determining required candles for ${pair.symbol}:`, error);
            return [];
        }
    }

    /**
     * كشف الفجوات في البيانات
     */
    detectGaps(existingData, startTime, endTime, timeframeMs) {
        const gaps = [];
        const sortedData = existingData.sort((a, b) => new Date(a.timestamp) - new Date(b.timestamp));
        
        let currentTime = startTime;
        let dataIndex = 0;
        
        while (currentTime < endTime) {
            const currentTimeStr = currentTime.toISOString();
            
            // البحث عن شمعة في هذا الوقت
            const existingCandle = sortedData.find(candle => 
                Math.abs(new Date(candle.timestamp) - currentTime) < timeframeMs / 2
            );
            
            if (!existingCandle) {
                gaps.push({
                    timestamp: currentTimeStr,
                    time: new Date(currentTime)
                });
            }
            
            currentTime = new Date(currentTime.getTime() + timeframeMs);
        }
        
        return gaps;
    }

    /**
     * توليد نطاق زمني
     */
    generateTimeRange(startTime, endTime, timeframeMs) {
        const range = [];
        let currentTime = new Date(startTime);
        
        while (currentTime < endTime) {
            range.push({
                timestamp: currentTime.toISOString(),
                time: new Date(currentTime)
            });
            currentTime = new Date(currentTime.getTime() + timeframeMs);
        }
        
        return range;
    }

    /**
     * جلب الشموع مع إعادة المحاولة
     */
    async fetchCandlesWithRetry(pair, requiredTimes) {
        let attempt = 0;
        
        while (attempt < this.options.retryAttempts) {
            try {
                const candles = await this.fetchCandlesFromAPI(pair, requiredTimes);
                return candles;
                
            } catch (error) {
                attempt++;
                console.warn(`⚠️ ${pair.symbol}: Attempt ${attempt} failed, retrying...`);
                
                if (attempt >= this.options.retryAttempts) {
                    throw error;
                }
                
                await this.delay(this.options.retryDelay * attempt);
            }
        }
    }

    /**
     * جلب الشموع من API
     */
    async fetchCandlesFromAPI(pair, requiredTimes) {
        try {
            // محاكاة جلب البيانات من Quotex API
            // في التطبيق الحقيقي، ستستخدم API الفعلي
            
            const candles = [];
            
            for (const timePoint of requiredTimes) {
                // محاكاة بيانات الشمعة
                const basePrice = 1.1000 + Math.random() * 0.1;
                const candle = {
                    assetId: pair.id,
                    assetSymbol: pair.symbol,
                    assetName: pair.name,
                    timestamp: timePoint.timestamp,
                    timeframe: this.options.timeframe,
                    open: basePrice,
                    high: basePrice + Math.random() * 0.001,
                    low: basePrice - Math.random() * 0.001,
                    close: basePrice + (Math.random() - 0.5) * 0.001,
                    volume: Math.floor(Math.random() * 1000) + 100,
                    isComplete: true,
                    metadata: {
                        source: 'quotex_api',
                        fetched: new Date().toISOString()
                    }
                };
                
                candle.high = Math.max(candle.open, candle.close, candle.high);
                candle.low = Math.min(candle.open, candle.close, candle.low);
                
                candles.push(candle);
            }
            
            // محاكاة تأخير الشبكة
            await this.delay(100 + Math.random() * 200);
            
            return candles;
            
        } catch (error) {
            console.error(`❌ API fetch error for ${pair.symbol}:`, error);
            throw error;
        }
    }

    /**
     * تحميل البيانات الموجودة
     */
    async loadExistingData(symbol) {
        try {
            const fileName = `${symbol}_historical.json`;
            const filePath = path.join(this.options.dataDir, fileName);
            
            const data = await fs.readFile(filePath, 'utf8');
            const parsed = JSON.parse(data);
            
            return parsed.candles || [];
            
        } catch (error) {
            // الملف غير موجود أو تالف
            return [];
        }
    }

    /**
     * دمج البيانات التاريخية
     */
    mergeHistoricalData(existingData, newCandles) {
        const allCandles = [...existingData, ...newCandles];
        
        // إزالة التكرارات بناء على الوقت
        const uniqueCandles = allCandles.filter((candle, index, array) => 
            array.findIndex(c => c.timestamp === candle.timestamp) === index
        );
        
        // ترتيب حسب الوقت
        return uniqueCandles.sort((a, b) => new Date(a.timestamp) - new Date(b.timestamp));
    }

    /**
     * حفظ البيانات التاريخية
     */
    async saveHistoricalData(symbol, candles) {
        try {
            const fileName = `${symbol}_historical.json`;
            const filePath = path.join(this.options.dataDir, fileName);
            
            const data = {
                symbol: symbol,
                timeframe: this.options.timeframe,
                lastUpdate: new Date().toISOString(),
                candlesCount: candles.length,
                candles: candles,
                metadata: {
                    source: 'historical_data_manager',
                    version: '1.0'
                }
            };
            
            await fs.writeFile(filePath, JSON.stringify(data, null, 2), 'utf8');
            
        } catch (error) {
            console.error(`❌ Error saving data for ${symbol}:`, error);
            throw error;
        }
    }

    /**
     * تقسيم المصفوفة إلى مجموعات
     */
    chunkArray(array, chunkSize) {
        const chunks = [];
        for (let i = 0; i < array.length; i += chunkSize) {
            chunks.push(array.slice(i, i + chunkSize));
        }
        return chunks;
    }

    /**
     * تأخير
     */
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * الحصول على التقدم
     */
    getProgress() {
        const elapsed = this.progress.startTime ? 
            (new Date() - this.progress.startTime) / 1000 : 0;
        
        const completionRate = this.progress.totalPairs > 0 ? 
            (this.progress.completedPairs / this.progress.totalPairs) * 100 : 0;
        
        const candleRate = this.progress.totalCandles > 0 ? 
            (this.progress.fetchedCandles / this.progress.totalCandles) * 100 : 0;
        
        return {
            ...this.progress,
            elapsedSeconds: Math.round(elapsed),
            completionRate: Math.round(completionRate * 100) / 100,
            candleRate: Math.round(candleRate * 100) / 100,
            estimatedTimeRemaining: this.estimateTimeRemaining(completionRate, elapsed)
        };
    }

    /**
     * تقدير الوقت المتبقي
     */
    estimateTimeRemaining(completionRate, elapsed) {
        if (completionRate <= 0) return null;
        
        const totalEstimated = (elapsed / completionRate) * 100;
        const remaining = totalEstimated - elapsed;
        
        return Math.max(0, Math.round(remaining));
    }

    /**
     * الحصول على إحصائيات البيانات
     */
    async getDataStatistics() {
        const stats = {
            totalPairs: this.targetPairs.length,
            pairsWithData: 0,
            totalCandles: 0,
            oldestCandle: null,
            newestCandle: null,
            averageCandlesPerPair: 0
        };

        for (const pair of this.targetPairs) {
            const data = await this.loadExistingData(pair.symbol);
            if (data.length > 0) {
                stats.pairsWithData++;
                stats.totalCandles += data.length;
                
                const sorted = data.sort((a, b) => new Date(a.timestamp) - new Date(b.timestamp));
                const oldest = sorted[0];
                const newest = sorted[sorted.length - 1];
                
                if (!stats.oldestCandle || new Date(oldest.timestamp) < new Date(stats.oldestCandle)) {
                    stats.oldestCandle = oldest.timestamp;
                }
                
                if (!stats.newestCandle || new Date(newest.timestamp) > new Date(stats.newestCandle)) {
                    stats.newestCandle = newest.timestamp;
                }
            }
        }

        stats.averageCandlesPerPair = stats.pairsWithData > 0 ? 
            Math.round(stats.totalCandles / stats.pairsWithData) : 0;

        return stats;
    }

    /**
     * إيقاف المدير
     */
    async shutdown() {
        try {
            console.log('🛑 Shutting down Historical Data Manager...');
            this.isProcessing = false;
            this.emit('shutdown');
            
        } catch (error) {
            console.error('❌ Error during shutdown:', error);
        }
    }
}

module.exports = HistoricalDataManager;
