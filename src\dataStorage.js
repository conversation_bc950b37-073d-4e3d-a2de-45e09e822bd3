/**
 * نظام تخزين البيانات في ملفات JSON
 * JSON Data Storage System
 */

const fs = require('fs').promises;
const path = require('path');
const EventEmitter = require('events');

class DataStorage extends EventEmitter {
    constructor(options = {}) {
        super();
        
        this.options = {
            dataDir: options.dataDir || './data',
            historicalDir: options.historicalDir || './data/historical',
            liveDir: options.liveDir || './data/live',
            analysisDir: options.analysisDir || './data/analysis',
            instrumentsFile: options.instrumentsFile || './data/instruments.json',
            profitRatesFile: options.profitRatesFile || './data/profit_rates.json',
            maxFileSize: options.maxFileSize || 50 * 1024 * 1024, // 50MB
            compressionEnabled: options.compressionEnabled || true,
            backupEnabled: options.backupEnabled || true,
            ...options
        };

        // كاش البيانات في الذاكرة
        this.cache = {
            instruments: new Map(),
            profitRates: new Map(),
            liveData: new Map(),
            analysis: new Map()
        };

        // إحصائيات
        this.stats = {
            filesCreated: 0,
            dataPointsStored: 0,
            lastSave: null,
            cacheHits: 0,
            cacheMisses: 0
        };

        // لا نستدعي initializeDirectories هنا، سنستدعيها في initialize
    }

    /**
     * تهيئة نظام التخزين
     */
    async initialize() {
        try {
            await this.initializeDirectories();
            console.log('✅ Data storage system initialized');
            return true;
        } catch (error) {
            console.error('❌ Failed to initialize data storage:', error);
            throw error;
        }
    }

    /**
     * تهيئة المجلدات
     */
    async initializeDirectories() {
        try {
            const dirs = [
                this.options.dataDir,
                this.options.historicalDir,
                this.options.liveDir,
                this.options.analysisDir
            ];

            for (const dir of dirs) {
                await this.ensureDirectoryExists(dir);
            }

            console.log('✅ Data storage directories initialized');
            this.emit('initialized');

        } catch (error) {
            console.error('❌ Failed to initialize directories:', error);
            this.emit('error', error);
        }
    }

    /**
     * التأكد من وجود المجلد
     */
    async ensureDirectoryExists(dirPath) {
        try {
            await fs.access(dirPath);
        } catch {
            await fs.mkdir(dirPath, { recursive: true });
            console.log(`📁 Created directory: ${dirPath}`);
        }
    }

    /**
     * حفظ البيانات التاريخية
     */
    async saveHistoricalData(assetId, timeframe, candles, analysis = null) {
        try {
            // التأكد من وجود المجلد
            await this.ensureDirectoryExists(this.options.historicalDir);

            const timestamp = new Date().toISOString().split('T')[0]; // YYYY-MM-DD
            const filename = `${assetId}_${timeframe}_${timestamp}.json`;
            const filepath = path.join(this.options.historicalDir, filename);

            const data = {
                assetId: assetId,
                timeframe: timeframe,
                timestamp: new Date().toISOString(),
                candlesCount: candles.length,
                candles: candles,
                analysis: analysis,
                metadata: {
                    source: 'quotex',
                    version: '1.0',
                    created: new Date().toISOString()
                }
            };

            await this.writeJsonFile(filepath, data);
            
            this.stats.filesCreated++;
            this.stats.dataPointsStored += candles.length;
            this.stats.lastSave = new Date();

            console.log(`💾 Saved historical data: ${filename} (${candles.length} candles)`);
            this.emit('historicalDataSaved', { assetId, timeframe, filename, count: candles.length });

            return filename;

        } catch (error) {
            console.error('❌ Error saving historical data:', error);
            this.emit('error', error);
            throw error;
        }
    }

    /**
     * حفظ البيانات المباشرة
     */
    async saveLiveData(assetId, candle, analysis = null) {
        try {
            const date = new Date();
            const dateStr = date.toISOString().split('T')[0];
            const hour = date.getHours().toString().padStart(2, '0');
            
            const filename = `${assetId}_live_${dateStr}_${hour}.json`;
            const filepath = path.join(this.options.liveDir, filename);

            // قراءة البيانات الموجودة أو إنشاء ملف جديد
            let existingData = { candles: [], analysis: [] };
            try {
                existingData = await this.readJsonFile(filepath);
            } catch {
                // الملف غير موجود، سننشئه
            }

            // إضافة الشمعة الجديدة
            const candleData = {
                ...candle,
                timestamp: new Date().toISOString(),
                analysis: analysis
            };

            existingData.candles.push(candleData);
            if (analysis) {
                existingData.analysis.push({
                    timestamp: new Date().toISOString(),
                    assetId: assetId,
                    ...analysis
                });
            }

            // تحديث البيانات الوصفية
            existingData.metadata = {
                assetId: assetId,
                lastUpdate: new Date().toISOString(),
                candlesCount: existingData.candles.length,
                source: 'quotex_live'
            };

            await this.writeJsonFile(filepath, existingData);

            // تحديث الكاش
            const cacheKey = `${assetId}_live`;
            this.cache.liveData.set(cacheKey, existingData);

            this.stats.dataPointsStored++;
            this.stats.lastSave = new Date();

            console.log(`📊 Saved live data: ${filename}`);
            this.emit('liveDataSaved', { assetId, filename, candle: candleData });

            return filename;

        } catch (error) {
            console.error('❌ Error saving live data:', error);
            this.emit('error', error);
            throw error;
        }
    }

    /**
     * حفظ تحليل المؤشرات
     */
    async saveAnalysis(assetId, timeframe, analysis) {
        try {
            const timestamp = new Date().toISOString();
            const dateStr = timestamp.split('T')[0];
            const filename = `analysis_${assetId}_${timeframe}_${dateStr}.json`;
            const filepath = path.join(this.options.analysisDir, filename);

            // قراءة التحليلات الموجودة
            let existingAnalysis = { analyses: [] };
            try {
                existingAnalysis = await this.readJsonFile(filepath);
            } catch {
                // الملف غير موجود
            }

            // إضافة التحليل الجديد
            const analysisData = {
                timestamp: timestamp,
                assetId: assetId,
                timeframe: timeframe,
                ...analysis
            };

            existingAnalysis.analyses.push(analysisData);
            existingAnalysis.metadata = {
                assetId: assetId,
                timeframe: timeframe,
                lastUpdate: timestamp,
                analysisCount: existingAnalysis.analyses.length
            };

            await this.writeJsonFile(filepath, existingAnalysis);

            // تحديث الكاش
            const cacheKey = `${assetId}_${timeframe}_analysis`;
            this.cache.analysis.set(cacheKey, existingAnalysis);

            console.log(`🔍 Saved analysis: ${filename}`);
            this.emit('analysisSaved', { assetId, timeframe, filename, analysis: analysisData });

            return filename;

        } catch (error) {
            console.error('❌ Error saving analysis:', error);
            this.emit('error', error);
            throw error;
        }
    }

    /**
     * حفظ قائمة الأدوات المالية
     */
    async saveInstruments(instruments) {
        try {
            // التأكد من وجود المجلد الأساسي
            await this.ensureDirectoryExists(this.options.dataDir);

            const data = {
                timestamp: new Date().toISOString(),
                count: instruments.length,
                instruments: instruments,
                metadata: {
                    source: 'quotex',
                    version: '1.0',
                    lastUpdate: new Date().toISOString()
                }
            };

            await this.writeJsonFile(this.options.instrumentsFile, data);

            // تحديث الكاش
            instruments.forEach(instrument => {
                this.cache.instruments.set(instrument.id, instrument);
            });

            console.log(`📋 Saved ${instruments.length} instruments`);
            this.emit('instrumentsSaved', { count: instruments.length });

            return this.options.instrumentsFile;

        } catch (error) {
            console.error('❌ Error saving instruments:', error);
            this.emit('error', error);
            throw error;
        }
    }

    /**
     * حفظ نسب الأرباح
     */
    async saveProfitRates(profitRates) {
        try {
            // التأكد من وجود المجلد الأساسي
            await this.ensureDirectoryExists(this.options.dataDir);

            const data = {
                timestamp: new Date().toISOString(),
                rates: profitRates,
                metadata: {
                    source: 'quotex',
                    ratesCount: Object.keys(profitRates).length,
                    lastUpdate: new Date().toISOString()
                }
            };

            await this.writeJsonFile(this.options.profitRatesFile, data);

            // تحديث الكاش
            for (const [assetId, rate] of Object.entries(profitRates)) {
                this.cache.profitRates.set(parseInt(assetId), rate);
            }

            console.log(`💰 Saved profit rates for ${Object.keys(profitRates).length} assets`);
            this.emit('profitRatesSaved', { count: Object.keys(profitRates).length });

            return this.options.profitRatesFile;

        } catch (error) {
            console.error('❌ Error saving profit rates:', error);
            this.emit('error', error);
            throw error;
        }
    }

    /**
     * قراءة البيانات التاريخية
     */
    async loadHistoricalData(assetId, timeframe, date = null) {
        try {
            const dateStr = date || new Date().toISOString().split('T')[0];
            const filename = `${assetId}_${timeframe}_${dateStr}.json`;
            const filepath = path.join(this.options.historicalDir, filename);

            const data = await this.readJsonFile(filepath);
            
            this.stats.cacheHits++;
            console.log(`📖 Loaded historical data: ${filename}`);
            
            return data;

        } catch (error) {
            this.stats.cacheMisses++;
            console.log(`⚠️ Historical data not found: ${assetId}_${timeframe}_${date || 'today'}`);
            return null;
        }
    }

    /**
     * قراءة البيانات المباشرة
     */
    async loadLiveData(assetId, date = null, hour = null) {
        try {
            const cacheKey = `${assetId}_live`;
            
            // التحقق من الكاش أولاً
            if (this.cache.liveData.has(cacheKey)) {
                this.stats.cacheHits++;
                return this.cache.liveData.get(cacheKey);
            }

            const dateStr = date || new Date().toISOString().split('T')[0];
            const hourStr = hour || new Date().getHours().toString().padStart(2, '0');
            const filename = `${assetId}_live_${dateStr}_${hourStr}.json`;
            const filepath = path.join(this.options.liveDir, filename);

            const data = await this.readJsonFile(filepath);
            
            // تحديث الكاش
            this.cache.liveData.set(cacheKey, data);
            
            this.stats.cacheHits++;
            console.log(`📊 Loaded live data: ${filename}`);
            
            return data;

        } catch (error) {
            this.stats.cacheMisses++;
            console.log(`⚠️ Live data not found: ${assetId}_${date || 'today'}_${hour || 'current'}`);
            return null;
        }
    }

    /**
     * قراءة قائمة الأدوات
     */
    async loadInstruments() {
        try {
            // التحقق من الكاش أولاً
            if (this.cache.instruments.size > 0) {
                this.stats.cacheHits++;
                return Array.from(this.cache.instruments.values());
            }

            const data = await this.readJsonFile(this.options.instrumentsFile);
            
            // تحديث الكاش
            data.instruments.forEach(instrument => {
                this.cache.instruments.set(instrument.id, instrument);
            });

            this.stats.cacheHits++;
            console.log(`📋 Loaded ${data.instruments.length} instruments from file`);
            
            return data.instruments;

        } catch (error) {
            this.stats.cacheMisses++;
            console.log('⚠️ Instruments file not found');
            return [];
        }
    }

    /**
     * قراءة نسب الأرباح
     */
    async loadProfitRates() {
        try {
            // التحقق من الكاش أولاً
            if (this.cache.profitRates.size > 0) {
                this.stats.cacheHits++;
                return Object.fromEntries(this.cache.profitRates);
            }

            const data = await this.readJsonFile(this.options.profitRatesFile);
            
            // تحديث الكاش
            for (const [assetId, rate] of Object.entries(data.rates)) {
                this.cache.profitRates.set(parseInt(assetId), rate);
            }

            this.stats.cacheHits++;
            console.log(`💰 Loaded profit rates for ${Object.keys(data.rates).length} assets`);
            
            return data.rates;

        } catch (error) {
            this.stats.cacheMisses++;
            console.log('⚠️ Profit rates file not found');
            return {};
        }
    }

    /**
     * كتابة ملف JSON
     */
    async writeJsonFile(filepath, data) {
        try {
            const jsonData = JSON.stringify(data, null, 2);
            
            // التحقق من حجم الملف
            if (jsonData.length > this.options.maxFileSize) {
                throw new Error(`File size exceeds limit: ${jsonData.length} bytes`);
            }

            await fs.writeFile(filepath, jsonData, 'utf8');
            
        } catch (error) {
            console.error(`❌ Error writing JSON file ${filepath}:`, error);
            throw error;
        }
    }

    /**
     * قراءة ملف JSON
     */
    async readJsonFile(filepath) {
        try {
            const data = await fs.readFile(filepath, 'utf8');
            return JSON.parse(data);
            
        } catch (error) {
            throw new Error(`Failed to read JSON file ${filepath}: ${error.message}`);
        }
    }

    /**
     * تنظيف الملفات القديمة
     */
    async cleanupOldFiles(daysToKeep = 30) {
        try {
            console.log(`🧹 Cleaning up files older than ${daysToKeep} days...`);
            
            const cutoffDate = new Date();
            cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);
            
            const dirs = [this.options.historicalDir, this.options.liveDir, this.options.analysisDir];
            let deletedCount = 0;

            for (const dir of dirs) {
                const files = await fs.readdir(dir);
                
                for (const file of files) {
                    const filepath = path.join(dir, file);
                    const stats = await fs.stat(filepath);
                    
                    if (stats.mtime < cutoffDate) {
                        await fs.unlink(filepath);
                        deletedCount++;
                        console.log(`🗑️ Deleted old file: ${file}`);
                    }
                }
            }

            console.log(`✅ Cleanup completed. Deleted ${deletedCount} old files`);
            this.emit('cleanupCompleted', { deletedCount });

        } catch (error) {
            console.error('❌ Error during cleanup:', error);
            this.emit('error', error);
        }
    }

    /**
     * الحصول على إحصائيات التخزين
     */
    getStorageStats() {
        return {
            ...this.stats,
            cacheSize: {
                instruments: this.cache.instruments.size,
                profitRates: this.cache.profitRates.size,
                liveData: this.cache.liveData.size,
                analysis: this.cache.analysis.size
            },
            cacheHitRate: this.stats.cacheHits / (this.stats.cacheHits + this.stats.cacheMisses) * 100
        };
    }

    /**
     * مسح الكاش
     */
    clearCache() {
        this.cache.instruments.clear();
        this.cache.profitRates.clear();
        this.cache.liveData.clear();
        this.cache.analysis.clear();
        
        console.log('🧹 Cache cleared');
        this.emit('cacheCleared');
    }
}

module.exports = DataStorage;
