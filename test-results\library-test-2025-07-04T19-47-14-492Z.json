{"passed": 44, "failed": 3, "total": 47, "details": [{"name": "Library Instance Creation", "passed": true, "message": "Library instance created successfully", "timestamp": "2025-07-04T19:47:14.450Z"}, {"name": "Library Initialization", "passed": true, "message": "Library initialized successfully", "timestamp": "2025-07-04T19:47:14.457Z"}, {"name": "Core Components", "passed": true, "message": "All core components initialized", "timestamp": "2025-07-04T19:47:14.457Z"}, {"name": "Library Status Check", "passed": true, "message": "Status retrieved successfully", "timestamp": "2025-07-04T19:47:14.458Z"}, {"name": "Initialization Status", "passed": true, "message": "Library is initialized", "timestamp": "2025-07-04T19:47:14.458Z"}, {"name": "Advanced Components", "passed": true, "message": "Advanced components initialized", "timestamp": "2025-07-04T19:47:14.459Z"}, {"name": "Target Pairs Retrieval", "passed": true, "message": "Target pairs retrieved", "timestamp": "2025-07-04T19:47:14.459Z"}, {"name": "Pairs Count", "passed": true, "message": "Expected 70 pairs, got 70", "timestamp": "2025-07-04T19:47:14.460Z"}, {"name": "Pair Data Structure", "passed": "major", "message": "Pairs have required fields", "timestamp": "2025-07-04T19:47:14.460Z"}, {"name": "Pair Categories", "passed": true, "message": "All expected categories present", "timestamp": "2025-07-04T19:47:14.460Z"}, {"name": "OTC Pairs", "passed": true, "message": "Found 49 OTC pairs", "timestamp": "2025-07-04T19:47:14.460Z"}, {"name": "Regular Pairs", "passed": true, "message": "Found 21 regular pairs", "timestamp": "2025-07-04T19:47:14.461Z"}, {"name": "Historical Manager Exists", "passed": true, "message": "Historical data manager initialized", "timestamp": "2025-07-04T19:47:14.461Z"}, {"name": "Manager Options", "passed": true, "message": "Manager has options configured", "timestamp": "2025-07-04T19:47:14.462Z"}, {"name": "Manager Target Pairs", "passed": false, "message": "Manager has 69 target pairs", "timestamp": "2025-07-04T19:47:14.462Z"}, {"name": "Progress Tracking", "passed": true, "message": "Progress tracking available", "timestamp": "2025-07-04T19:47:14.462Z"}, {"name": "Data Statistics", "passed": true, "message": "Data statistics retrieved", "timestamp": "2025-07-04T19:47:14.481Z"}, {"name": "Analysis Engine Exists", "passed": true, "message": "Analysis engine initialized", "timestamp": "2025-07-04T19:47:14.482Z"}, {"name": "Engine Options", "passed": true, "message": "Engine has options configured", "timestamp": "2025-07-04T19:47:14.482Z"}, {"name": "Confidence Level", "passed": true, "message": "Confidence level: 80%", "timestamp": "2025-07-04T19:47:14.483Z"}, {"name": "Technical Indicators", "passed": true, "message": "Technical indicators available", "timestamp": "2025-07-04T19:47:14.483Z"}, {"name": "Performance Stats", "passed": true, "message": "Performance statistics available", "timestamp": "2025-07-04T19:47:14.483Z"}, {"name": "Analysis Results", "passed": true, "message": "Analysis results structure correct", "timestamp": "2025-07-04T19:47:14.483Z"}, {"name": "Auto Trader Exists", "passed": true, "message": "Auto trader initialized", "timestamp": "2025-07-04T19:47:14.484Z"}, {"name": "Trader Options", "passed": true, "message": "Trader has options configured", "timestamp": "2025-07-04T19:47:14.484Z"}, {"name": "Trading Status", "passed": true, "message": "Trading status available", "timestamp": "2025-07-04T19:47:14.484Z"}, {"name": "Risk Management", "passed": 3, "message": "Risk management settings configured", "timestamp": "2025-07-04T19:47:14.484Z"}, {"name": "Trader Stats", "passed": true, "message": "Trader statistics available", "timestamp": "2025-07-04T19:47:14.484Z"}, {"name": "Live Streamer Exists", "passed": true, "message": "Live data streamer initialized", "timestamp": "2025-07-04T19:47:14.485Z"}, {"name": "Streamer Options", "passed": true, "message": "Streamer has options configured", "timestamp": "2025-07-04T19:47:14.485Z"}, {"name": "Streaming Port", "passed": true, "message": "Port configured: 8081", "timestamp": "2025-07-04T19:47:14.485Z"}, {"name": "Live Data Structure", "passed": true, "message": "Live data structure initialized", "timestamp": "2025-07-04T19:47:14.485Z"}, {"name": "Streaming Stats", "passed": true, "message": "Streaming statistics available", "timestamp": "2025-07-04T19:47:14.485Z"}, {"name": "Session Manager Exists", "passed": true, "message": "Session manager initialized", "timestamp": "2025-07-04T19:47:14.486Z"}, {"name": "Session Options", "passed": true, "message": "Session manager has options", "timestamp": "2025-07-04T19:47:14.486Z"}, {"name": "Session Stats", "passed": true, "message": "Session statistics available", "timestamp": "2025-07-04T19:47:14.486Z"}, {"name": "Session Initialization", "passed": false, "message": "Session manager initialized", "timestamp": "2025-07-04T19:47:14.486Z"}, {"name": "Trade Manager Exists", "passed": true, "message": "Trade manager initialized", "timestamp": "2025-07-04T19:47:14.487Z"}, {"name": "Trade Options", "passed": true, "message": "Trade manager has options", "timestamp": "2025-07-04T19:47:14.487Z"}, {"name": "Trade Stats", "passed": true, "message": "Trade statistics available", "timestamp": "2025-07-04T19:47:14.487Z"}, {"name": "Open Trades", "passed": true, "message": "Open trades structure correct", "timestamp": "2025-07-04T19:47:14.488Z"}, {"name": "Closed Trades", "passed": true, "message": "Closed trades structure correct", "timestamp": "2025-07-04T19:47:14.488Z"}, {"name": "Candle Manager Exists", "passed": true, "message": "Candle manager initialized", "timestamp": "2025-07-04T19:47:14.488Z"}, {"name": "Candle Options", "passed": true, "message": "Candle manager has options", "timestamp": "2025-07-04T19:47:14.489Z"}, {"name": "Max Live Candles", "passed": true, "message": "Max live candles: 100", "timestamp": "2025-07-04T19:47:14.489Z"}, {"name": "Candle Stats", "passed": true, "message": "Candle statistics available", "timestamp": "2025-07-04T19:47:14.489Z"}, {"name": "Candle Initialization", "passed": false, "message": "Candle manager initialized", "timestamp": "2025-07-04T19:47:14.489Z"}]}