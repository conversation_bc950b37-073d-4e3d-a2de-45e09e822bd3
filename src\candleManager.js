/**
 * مدير الشموع مع نظام FIFO
 * Candle Manager with FIFO System
 */

const fs = require('fs').promises;
const path = require('path');
const EventEmitter = require('events');

class CandleManager extends EventEmitter {
    constructor(options = {}) {
        super();
        
        this.options = {
            dataDir: options.dataDir || './data',
            liveDir: options.liveDir || './data/live',
            historicalDir: options.historicalDir || './data/historical',
            maxLiveCandles: options.maxLiveCandles || 100, // عدد الشموع الحية لكل زوج
            maxHistoricalCandles: options.maxHistoricalCandles || 10000, // عدد الشموع التاريخية لكل زوج
            autoSave: options.autoSave !== false,
            saveInterval: options.saveInterval || 60000, // حفظ كل دقيقة
            enableCompression: options.enableCompression || false,
            ...options
        };

        // تخزين الشموع الحية لكل زوج (FIFO)
        this.liveCandles = new Map(); // assetId -> Array of candles
        
        // تخزين الشموع المغلقة مؤقتاً قبل النقل للملفات
        this.closedCandles = new Map(); // assetId -> Array of closed candles
        
        // معلومات الأزواج
        this.assetInfo = new Map(); // assetId -> asset info
        
        // إحصائيات
        this.stats = {
            totalLiveCandles: 0,
            totalClosedCandles: 0,
            lastSave: null,
            lastCleanup: null,
            candlesProcessed: 0
        };

        // مؤقت الحفظ التلقائي
        this.saveTimer = null;
        
        this.isInitialized = false;
    }

    /**
     * تهيئة مدير الشموع
     */
    async initialize() {
        try {
            console.log('🕯️ Initializing Candle Manager...');
            
            // إنشاء المجلدات المطلوبة
            await this.ensureDirectories();
            
            // تحميل الشموع الحية المحفوظة
            await this.loadLiveCandles();
            
            // بدء الحفظ التلقائي
            if (this.options.autoSave) {
                this.startAutoSave();
            }
            
            this.isInitialized = true;
            console.log('✅ Candle Manager initialized successfully');
            this.emit('initialized');
            
            return true;
            
        } catch (error) {
            console.error('❌ Failed to initialize Candle Manager:', error);
            this.emit('error', error);
            throw error;
        }
    }

    /**
     * التأكد من وجود المجلدات
     */
    async ensureDirectories() {
        const dirs = [this.options.dataDir, this.options.liveDir, this.options.historicalDir];
        
        for (const dir of dirs) {
            try {
                await fs.access(dir);
            } catch {
                await fs.mkdir(dir, { recursive: true });
                console.log(`📁 Created directory: ${dir}`);
            }
        }
    }

    /**
     * إضافة شمعة جديدة (FIFO)
     */
    async addCandle(assetId, candleData, assetInfo = null) {
        try {
            // تحديث معلومات الأصل
            if (assetInfo) {
                this.assetInfo.set(assetId, assetInfo);
            }

            // إنشاء الشمعة مع البيانات الكاملة
            const candle = {
                assetId: assetId,
                timestamp: candleData.timestamp || new Date().toISOString(),
                open: candleData.open,
                high: candleData.high,
                low: candleData.low,
                close: candleData.close,
                volume: candleData.volume || 0,
                timeframe: candleData.timeframe || 60, // ثانية
                isComplete: candleData.isComplete || false,
                
                // بيانات إضافية
                spread: candleData.spread || 0,
                tickCount: candleData.tickCount || 0,
                
                // معلومات الأصل
                assetName: assetInfo?.name || this.assetInfo.get(assetId)?.name,
                assetSymbol: assetInfo?.symbol || this.assetInfo.get(assetId)?.symbol,
                
                metadata: {
                    created: new Date().toISOString(),
                    source: 'quotex_live'
                }
            };

            // الحصول على مصفوفة الشموع الحية للأصل
            if (!this.liveCandles.has(assetId)) {
                this.liveCandles.set(assetId, []);
            }

            const liveCandlesArray = this.liveCandles.get(assetId);

            // إضافة الشمعة الجديدة
            liveCandlesArray.push(candle);

            // تطبيق نظام FIFO - إزالة الشموع الزائدة
            while (liveCandlesArray.length > this.options.maxLiveCandles) {
                const removedCandle = liveCandlesArray.shift(); // إزالة الأقدم
                
                // نقل الشمعة المزالة إلى الشموع المغلقة
                if (removedCandle.isComplete) {
                    await this.moveToClosedCandles(assetId, removedCandle);
                }
            }

            // تحديث الإحصائيات
            this.stats.totalLiveCandles = this.getTotalLiveCandles();
            this.stats.candlesProcessed++;

            console.log(`🕯️ Added candle for asset ${assetId} (${liveCandlesArray.length}/${this.options.maxLiveCandles})`);
            this.emit('candleAdded', { assetId, candle, totalLive: liveCandlesArray.length });

            return candle;
            
        } catch (error) {
            console.error('❌ Error adding candle:', error);
            this.emit('error', error);
            throw error;
        }
    }

    /**
     * تحديث الشمعة الحالية (الحية)
     */
    async updateCurrentCandle(assetId, updateData) {
        try {
            const liveCandlesArray = this.liveCandles.get(assetId);
            if (!liveCandlesArray || liveCandlesArray.length === 0) {
                // إنشاء شمعة جديدة إذا لم توجد
                return await this.addCandle(assetId, updateData);
            }

            // تحديث آخر شمعة (الحالية)
            const currentCandle = liveCandlesArray[liveCandlesArray.length - 1];
            
            // تحديث البيانات
            Object.assign(currentCandle, {
                high: Math.max(currentCandle.high, updateData.high || currentCandle.high),
                low: Math.min(currentCandle.low, updateData.low || currentCandle.low),
                close: updateData.close || currentCandle.close,
                volume: updateData.volume || currentCandle.volume,
                isComplete: updateData.isComplete || currentCandle.isComplete,
                metadata: {
                    ...currentCandle.metadata,
                    lastUpdate: new Date().toISOString()
                }
            });

            this.emit('candleUpdated', { assetId, candle: currentCandle });

            return currentCandle;
            
        } catch (error) {
            console.error('❌ Error updating current candle:', error);
            this.emit('error', error);
            throw error;
        }
    }

    /**
     * إغلاق الشمعة الحالية وبدء شمعة جديدة
     */
    async closeCurrentCandle(assetId, finalData = {}) {
        try {
            const liveCandlesArray = this.liveCandles.get(assetId);
            if (!liveCandlesArray || liveCandlesArray.length === 0) {
                console.log(`⚠️ No current candle to close for asset ${assetId}`);
                return null;
            }

            // إغلاق آخر شمعة
            const currentCandle = liveCandlesArray[liveCandlesArray.length - 1];
            
            // تحديث بيانات الإغلاق
            Object.assign(currentCandle, {
                ...finalData,
                isComplete: true,
                metadata: {
                    ...currentCandle.metadata,
                    closedAt: new Date().toISOString()
                }
            });

            console.log(`🏁 Closed candle for asset ${assetId}: ${currentCandle.open} -> ${currentCandle.close}`);
            this.emit('candleClosed', { assetId, candle: currentCandle });

            return currentCandle;
            
        } catch (error) {
            console.error('❌ Error closing current candle:', error);
            this.emit('error', error);
            throw error;
        }
    }

    /**
     * نقل الشمعة إلى الشموع المغلقة
     */
    async moveToClosedCandles(assetId, candle) {
        try {
            if (!this.closedCandles.has(assetId)) {
                this.closedCandles.set(assetId, []);
            }

            const closedCandlesArray = this.closedCandles.get(assetId);
            closedCandlesArray.push(candle);

            // حفظ الشموع المغلقة إذا وصلت لحد معين
            if (closedCandlesArray.length >= 50) {
                await this.saveClosedCandles(assetId);
            }

            this.stats.totalClosedCandles++;
            this.emit('candleMovedToClosed', { assetId, candle });
            
        } catch (error) {
            console.error('❌ Error moving candle to closed:', error);
        }
    }

    /**
     * الحصول على الشموع الحية لأصل معين
     */
    getLiveCandles(assetId, limit = null) {
        const candles = this.liveCandles.get(assetId) || [];
        return limit ? candles.slice(-limit) : candles;
    }

    /**
     * الحصول على آخر شمعة حية
     */
    getCurrentCandle(assetId) {
        const candles = this.liveCandles.get(assetId) || [];
        return candles.length > 0 ? candles[candles.length - 1] : null;
    }

    /**
     * الحصول على الشموع المغلقة من الملف
     */
    async getHistoricalCandles(assetId, limit = 100) {
        try {
            const fileName = `${assetId}_historical.json`;
            const filePath = path.join(this.options.historicalDir, fileName);
            
            const data = await this.readJsonFile(filePath);
            if (data && data.candles) {
                return data.candles.slice(-limit);
            }
            
            return [];
            
        } catch (error) {
            console.log(`📝 No historical data found for asset ${assetId}`);
            return [];
        }
    }

    /**
     * حفظ الشموع الحية
     */
    async saveLiveCandles() {
        try {
            for (const [assetId, candles] of this.liveCandles) {
                const fileName = `${assetId}_live.json`;
                const filePath = path.join(this.options.liveDir, fileName);
                
                const data = {
                    assetId: assetId,
                    assetInfo: this.assetInfo.get(assetId),
                    timestamp: new Date().toISOString(),
                    candlesCount: candles.length,
                    candles: candles,
                    metadata: {
                        maxCandles: this.options.maxLiveCandles,
                        source: 'candle_manager'
                    }
                };

                await this.writeJsonFile(filePath, data);
            }

            this.stats.lastSave = new Date();
            console.log(`💾 Saved live candles for ${this.liveCandles.size} assets`);
            this.emit('liveCandlesSaved');
            
        } catch (error) {
            console.error('❌ Error saving live candles:', error);
            throw error;
        }
    }

    /**
     * حفظ الشموع المغلقة
     */
    async saveClosedCandles(assetId) {
        try {
            const closedCandlesArray = this.closedCandles.get(assetId);
            if (!closedCandlesArray || closedCandlesArray.length === 0) {
                return;
            }

            const fileName = `${assetId}_historical.json`;
            const filePath = path.join(this.options.historicalDir, fileName);
            
            // تحميل البيانات الموجودة
            let existingData = { candles: [] };
            try {
                existingData = await this.readJsonFile(filePath);
            } catch {
                // الملف غير موجود
            }

            // إضافة الشموع الجديدة
            const allCandles = [...(existingData.candles || []), ...closedCandlesArray];
            
            // الاحتفاظ بآخر شموع فقط
            const limitedCandles = allCandles.slice(-this.options.maxHistoricalCandles);

            const data = {
                assetId: assetId,
                assetInfo: this.assetInfo.get(assetId),
                timestamp: new Date().toISOString(),
                candlesCount: limitedCandles.length,
                candles: limitedCandles,
                metadata: {
                    maxCandles: this.options.maxHistoricalCandles,
                    source: 'candle_manager'
                }
            };

            await this.writeJsonFile(filePath, data);
            
            // مسح الشموع المغلقة من الذاكرة بعد الحفظ
            this.closedCandles.set(assetId, []);
            
            console.log(`💾 Saved ${closedCandlesArray.length} closed candles for asset ${assetId}`);
            this.emit('closedCandlesSaved', { assetId, count: closedCandlesArray.length });
            
        } catch (error) {
            console.error('❌ Error saving closed candles:', error);
            throw error;
        }
    }

    /**
     * تحميل الشموع الحية
     */
    async loadLiveCandles() {
        try {
            const files = await fs.readdir(this.options.liveDir);
            const liveFiles = files.filter(file => file.endsWith('_live.json'));
            
            for (const file of liveFiles) {
                const filePath = path.join(this.options.liveDir, file);
                const data = await this.readJsonFile(filePath);
                
                if (data && data.candles) {
                    this.liveCandles.set(data.assetId, data.candles);
                    if (data.assetInfo) {
                        this.assetInfo.set(data.assetId, data.assetInfo);
                    }
                }
            }

            this.stats.totalLiveCandles = this.getTotalLiveCandles();
            console.log(`📖 Loaded live candles for ${this.liveCandles.size} assets`);
            
        } catch (error) {
            console.log('📝 No live candles found, starting fresh');
        }
    }

    /**
     * بدء الحفظ التلقائي
     */
    startAutoSave() {
        if (this.saveTimer) {
            clearInterval(this.saveTimer);
        }

        this.saveTimer = setInterval(async () => {
            try {
                await this.saveLiveCandles();
                
                // حفظ الشموع المغلقة للأصول التي لديها شموع مغلقة
                for (const assetId of this.closedCandles.keys()) {
                    await this.saveClosedCandles(assetId);
                }
                
            } catch (error) {
                console.error('❌ Error in auto save:', error);
            }
        }, this.options.saveInterval);

        console.log(`⏰ Auto save started (interval: ${this.options.saveInterval}ms)`);
    }

    /**
     * إيقاف الحفظ التلقائي
     */
    stopAutoSave() {
        if (this.saveTimer) {
            clearInterval(this.saveTimer);
            this.saveTimer = null;
            console.log('⏰ Auto save stopped');
        }
    }

    /**
     * الحصول على العدد الإجمالي للشموع الحية
     */
    getTotalLiveCandles() {
        let total = 0;
        for (const candles of this.liveCandles.values()) {
            total += candles.length;
        }
        return total;
    }

    /**
     * تنظيف البيانات القديمة
     */
    async cleanup() {
        try {
            // حفظ جميع الشموع المغلقة
            for (const assetId of this.closedCandles.keys()) {
                await this.saveClosedCandles(assetId);
            }

            // حفظ الشموع الحية
            await this.saveLiveCandles();

            this.stats.lastCleanup = new Date();
            console.log('🧹 Candle Manager cleanup completed');
            this.emit('cleanupCompleted');
            
        } catch (error) {
            console.error('❌ Error during cleanup:', error);
        }
    }

    /**
     * كتابة ملف JSON
     */
    async writeJsonFile(filepath, data) {
        const jsonData = JSON.stringify(data, null, 2);
        await fs.writeFile(filepath, jsonData, 'utf8');
    }

    /**
     * قراءة ملف JSON
     */
    async readJsonFile(filepath) {
        const data = await fs.readFile(filepath, 'utf8');
        return JSON.parse(data);
    }

    /**
     * الحصول على إحصائيات مدير الشموع
     */
    getStats() {
        return {
            ...this.stats,
            assetsCount: this.liveCandles.size,
            totalLiveCandles: this.getTotalLiveCandles(),
            totalClosedCandlesInMemory: Array.from(this.closedCandles.values()).reduce((sum, arr) => sum + arr.length, 0),
            isInitialized: this.isInitialized
        };
    }

    /**
     * إيقاف مدير الشموع
     */
    async shutdown() {
        try {
            console.log('🛑 Shutting down Candle Manager...');
            
            // إيقاف الحفظ التلقائي
            this.stopAutoSave();
            
            // حفظ جميع البيانات
            await this.cleanup();
            
            console.log('✅ Candle Manager shutdown completed');
            this.emit('shutdown');
            
        } catch (error) {
            console.error('❌ Error during shutdown:', error);
        }
    }
}

module.exports = CandleManager;
