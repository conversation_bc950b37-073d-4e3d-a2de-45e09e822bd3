/**
 * موصل منصة Quotex الحقيقي
 * Real Quotex Platform Connector
 */

const puppeteer = require('puppeteer-extra');
const StealthPlugin = require('puppeteer-extra-plugin-stealth');
const WebSocket = require('ws');
const EventEmitter = require('events');
const SessionManager = require('./sessionManager');
const TradeManager = require('./tradeManager');
const CandleManager = require('./candleManager');

// استخدام plugin التخفي لتجنب الكشف
puppeteer.use(StealthPlugin());

class QuotexConnector extends EventEmitter {
    constructor(options = {}) {
        super();
        
        this.options = {
            headless: options.headless !== false, // افتراضي true
            userDataDir: options.userDataDir || './user_data',
            timeout: options.timeout || 30000,
            ...options
        };
        
        this.browser = null;
        this.page = null;
        this.wsConnection = null;
        this.isConnected = false;
        this.isAuthenticated = false;
        
        // بيانات الحساب
        this.accountData = {
            balance: 0,
            isDemo: true,
            userId: null,
            sessionId: null
        };
        
        // بيانات الأدوات المالية
        this.instruments = new Map();
        this.profitRates = new Map();

        // قائمة الأزواج الـ70 المحددة
        this.targetPairs = this.initializeTargetPairs();

        // مدير الجلسات والكوكيز
        this.sessionManager = new SessionManager({
            sessionDir: './sessions',
            maxSessionAge: 24 * 60 * 60 * 1000 // 24 ساعة
        });

        // مدير الصفقات
        this.tradeManager = new TradeManager({
            tradesDir: './data/trades'
        });

        // مدير الشموع مع نظام FIFO
        this.candleManager = new CandleManager({
            dataDir: './data',
            maxLiveCandles: 100, // 100 شمعة حية لكل زوج
            maxHistoricalCandles: 10000 // 10000 شمعة تاريخية لكل زوج
        });

        // معالجة الرسائل
        this.messageHandlers = new Map();
        this.setupMessageHandlers();
    }

    /**
     * بدء الاتصال بمنصة Quotex مع فتح صفحة تسجيل الدخول
     */
    async connect() {
        try {
            console.log('🚀 Opening Quotex login page...');

            // تهيئة مدير الجلسات
            await this.sessionManager.initialize();

            // تهيئة مدير الصفقات
            await this.tradeManager.initialize();

            // تهيئة مدير الشموع
            await this.candleManager.initialize();

            // التحقق من وجود جلسة صالحة
            const existingSession = this.sessionManager.getCurrentSession();
            if (existingSession) {
                console.log('🔓 Found valid session, attempting to restore...');
                const restored = await this.restoreSession(existingSession);
                if (restored) {
                    return true;
                }
            }

            // إطلاق المتصفح (دائماً مرئي)
            this.browser = await puppeteer.launch({
                headless: false, // دائماً مرئي لتسجيل الدخول اليدوي
                defaultViewport: null,
                userDataDir: this.options.userDataDir,
                args: [
                    '--start-maximized',
                    '--no-sandbox',
                    '--disable-setuid-sandbox',
                    '--disable-web-security',
                    '--disable-features=VizDisplayCompositor'
                ]
            });

            this.page = await this.browser.newPage();

            // تمكين اعتراض الطلبات لمراقبة API
            await this.page.setRequestInterception(true);

            // مراقبة طلبات API
            this.page.on('request', request => {
                const url = request.url();
                if (url.includes('/api/') || url.includes('/ws/') || url.includes('socket')) {
                    console.log('📡 API Request:', url);
                }
                request.continue();
            });

            // مراقبة استجابات API
            this.page.on('response', response => {
                const url = response.url();
                if (url.includes('/api/') || url.includes('ssid') || url.includes('session')) {
                    console.log('📥 API Response:', url, response.status());
                }
            });

            // تعيين User Agent
            await this.page.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36');

            // الانتقال لصفحة تسجيل الدخول
            console.log('📱 Opening Quotex login page...');
            await this.page.goto('https://qxbroker.com/en/sign-in', {
                waitUntil: 'networkidle2',
                timeout: this.options.timeout
            });

            console.log('👤 Please login manually in the browser window...');
            console.log('⏳ Waiting for successful login...');

            // انتظار تسجيل الدخول اليدوي
            await this.waitForManualLogin();

            // انتظار تحميل المنصة
            await this.waitForPlatform();

            // إعداد اتصال WebSocket
            await this.setupWebSocket();

            // جلب بيانات الحساب والأدوات
            await this.loadAccountData();
            await this.loadInstruments();

            // حفظ الجلسة والكوكيز
            await this.saveCurrentSession();

            this.isConnected = true;
            this.emit('connected');

            console.log('✅ Successfully connected to Quotex platform');
            return true;
            
        } catch (error) {
            console.error('❌ Failed to connect to Quotex:', error);
            this.emit('error', error);
            throw error;
        }
    }

    /**
     * تسجيل الدخول
     */
    async login(email, password) {
        try {
            console.log('🔐 Logging in to Quotex...');
            
            // انتظار ظهور حقول تسجيل الدخول
            await this.page.waitForSelector('input[name="email"]', { timeout: 10000 });
            await this.page.waitForSelector('input[name="password"]', { timeout: 10000 });
            
            // إدخال البيانات
            await this.page.type('input[name="email"]', email);
            await this.page.type('input[name="password"]', password);
            
            // النقر على زر تسجيل الدخول
            await this.page.click('button[type="submit"]');
            
            // انتظار إعادة التوجيه
            await this.page.waitForNavigation({ 
                waitUntil: 'networkidle2',
                timeout: this.options.timeout 
            });
            
            console.log('✅ Login successful');
            
        } catch (error) {
            console.error('❌ Login failed:', error);
            throw new Error('Login failed: ' + error.message);
        }
    }

    /**
     * انتظار تسجيل الدخول اليدوي
     */
    async waitForManualLogin() {
        return new Promise((resolve, reject) => {
            const timeout = setTimeout(() => {
                reject(new Error('Login timeout - Please login within 5 minutes'));
            }, 300000); // 5 دقائق

            // مراقبة تغيير URL للكشف عن تسجيل الدخول الناجح
            const checkLogin = async () => {
                try {
                    const currentUrl = this.page.url();

                    // إذا تم التحويل إلى صفحة التداول
                    if (currentUrl.includes('/trade') || currentUrl.includes('/demo') ||
                        currentUrl.includes('/platform') || currentUrl.includes('/trading')) {

                        console.log('✅ Login successful! Redirected to trading platform');
                        clearTimeout(timeout);
                        resolve();
                        return;
                    }

                    // إذا كان هناك عنصر يدل على تسجيل الدخول الناجح
                    const loggedInElement = await this.page.$('.user-info, .account-balance, .trading-panel, [data-testid="balance"]');
                    if (loggedInElement) {
                        console.log('✅ Login successful! Found logged-in elements');
                        clearTimeout(timeout);
                        resolve();
                        return;
                    }

                    // إعادة المحاولة بعد ثانية واحدة
                    setTimeout(checkLogin, 1000);

                } catch (error) {
                    console.error('Error checking login status:', error);
                    setTimeout(checkLogin, 2000);
                }
            };

            // بدء مراقبة تسجيل الدخول
            checkLogin();
        });
    }

    /**
     * انتظار تحميل منصة التداول
     */
    async waitForPlatform() {
        try {
            console.log('⏳ Waiting for trading platform to load...');

            // قائمة محددات مختلفة للبحث عن منصة التداول
            const selectors = [
                '.trading-view',
                '.trade-panel',
                '.trading-panel',
                '.chart-container',
                '.trading-interface',
                '.asset-select',
                '[data-testid="trading-panel"]',
                '.main-trading-area',
                '.trading-workspace',
                '.chart-wrapper',
                '.trade-form'
            ];

            let platformFound = false;

            // محاولة العثور على أي من العناصر
            for (const selector of selectors) {
                try {
                    await this.page.waitForSelector(selector, { timeout: 3000 });
                    console.log(`✅ Found platform element: ${selector}`);
                    platformFound = true;
                    break;
                } catch (error) {
                    // تجربة المحدد التالي
                    continue;
                }
            }

            // إذا لم نجد عناصر، نتحقق من URL
            if (!platformFound) {
                const currentUrl = this.page.url();
                if (currentUrl.includes('/trade') || currentUrl.includes('/demo') ||
                    currentUrl.includes('/platform') || currentUrl.includes('/trading')) {
                    console.log('✅ Platform detected by URL pattern');
                    platformFound = true;
                }
            }

            // انتظار WebSocket إذا أمكن
            try {
                await this.page.waitForFunction(() => {
                    return window.WebSocket && window.WebSocket.prototype;
                }, { timeout: 5000 });
                console.log('✅ WebSocket support detected');
            } catch (error) {
                console.log('⚠️ WebSocket detection failed, continuing...');
            }

            // انتظار إضافي قصير
            await this.page.waitForTimeout(2000);

            console.log('✅ Trading platform ready');

        } catch (error) {
            console.error('❌ Platform loading failed:', error);
            // لا نرمي خطأ، نتابع العمل
            console.log('⚠️ Continuing without full platform detection...');
        }
    }

    /**
     * إعداد اتصال WebSocket
     */
    async setupWebSocket() {
        try {
            console.log('🔌 Setting up WebSocket connection...');
            
            // الحصول على معلومات WebSocket من الصفحة
            const wsInfo = await this.page.evaluate(() => {
                // البحث عن اتصال WebSocket في الصفحة
                if (window.wsConnection) {
                    return {
                        url: window.wsConnection.url,
                        sessionId: window.sessionId || null
                    };
                }
                return null;
            });

            if (!wsInfo) {
                // إنشاء اتصال WebSocket جديد
                const wsUrl = 'wss://ws.qxbroker.com/socket.io/?EIO=3&transport=websocket';
                this.wsConnection = new WebSocket(wsUrl);
                
                this.wsConnection.on('open', () => {
                    console.log('✅ WebSocket connected');
                    this.emit('wsConnected');
                });

                this.wsConnection.on('message', (data) => {
                    this.handleWebSocketMessage(data.toString());
                });

                this.wsConnection.on('error', (error) => {
                    console.error('❌ WebSocket error:', error);
                    this.emit('wsError', error);
                });

                this.wsConnection.on('close', () => {
                    console.log('🔌 WebSocket disconnected');
                    this.emit('wsDisconnected');
                });
            }
            
        } catch (error) {
            console.error('❌ WebSocket setup failed:', error);
            throw error;
        }
    }

    /**
     * معالجة رسائل WebSocket
     */
    handleWebSocketMessage(message) {
        try {
            // تحليل رسائل Socket.IO
            if (message === '0') {
                console.log('🔗 WebSocket handshake completed');
                return;
            }
            
            if (message === '40') {
                console.log('✅ WebSocket authenticated');
                this.isAuthenticated = true;
                this.emit('authenticated');
                return;
            }
            
            if (message.startsWith('42')) {
                const data = JSON.parse(message.substring(2));
                const event = data[0];
                const payload = data[1];
                
                // توجيه الرسالة للمعالج المناسب
                if (this.messageHandlers.has(event)) {
                    this.messageHandlers.get(event)(payload);
                }
                
                this.emit('message', { event, payload });
            }
            
        } catch (error) {
            console.error('❌ Error handling WebSocket message:', error);
        }
    }

    /**
     * إعداد معالجات الرسائل
     */
    setupMessageHandlers() {
        // معالج بيانات الأسعار المباشرة
        this.messageHandlers.set('price', (data) => {
            this.emit('priceUpdate', data);
        });

        // معالج بيانات الشموع
        this.messageHandlers.set('candle', async (data) => {
            try {
                // الحصول على معلومات الأصل
                const assetInfo = this.instruments.get(data.assetId) || {
                    name: this.getAssetName(data.assetId),
                    symbol: data.assetId
                };

                // إضافة أو تحديث الشمعة في مدير الشموع
                if (data.isNewCandle) {
                    // شمعة جديدة - إغلاق السابقة وإضافة الجديدة
                    await this.candleManager.closeCurrentCandle(data.assetId, {
                        close: data.previousClose,
                        volume: data.previousVolume
                    });

                    await this.candleManager.addCandle(data.assetId, {
                        timestamp: data.timestamp,
                        open: data.open,
                        high: data.high,
                        low: data.low,
                        close: data.close,
                        volume: data.volume || 0,
                        timeframe: data.timeframe || 60,
                        isComplete: false
                    }, assetInfo);
                } else {
                    // تحديث الشمعة الحالية
                    await this.candleManager.updateCurrentCandle(data.assetId, {
                        high: data.high,
                        low: data.low,
                        close: data.close,
                        volume: data.volume || 0,
                        isComplete: data.isComplete || false
                    });
                }

                this.emit('candleUpdate', data);

            } catch (error) {
                console.error('❌ Error processing candle data:', error);
                this.emit('candleUpdate', data);
            }
        });

        // معالج رصيد الحساب
        this.messageHandlers.set('balance', (data) => {
            this.accountData.balance = data.balance;
            this.emit('balanceUpdate', data);

            // تحديث الجلسة مع الرصيد الجديد
            this.sessionManager.updateSessionActivity({
                balance: data.balance
            }).catch(console.error);
        });

        // معالج نسب الأرباح
        this.messageHandlers.set('profit_rates', (data) => {
            if (data && data.rates) {
                for (const [assetId, rate] of Object.entries(data.rates)) {
                    this.profitRates.set(parseInt(assetId), rate);
                }
                this.emit('profitRatesUpdate', data);
            }
        });

        // معالج نتائج الصفقات
        this.messageHandlers.set('trade_result', async (data) => {
            try {
                // البحث عن الصفقة في مدير الصفقات
                const openTrades = this.tradeManager.getOpenTrades();
                const matchingTrade = openTrades.find(trade =>
                    trade.assetId === data.assetId &&
                    Math.abs(new Date(trade.openTime).getTime() - data.openTime) < 60000 // خلال دقيقة
                );

                if (matchingTrade) {
                    // إغلاق الصفقة في مدير الصفقات
                    await this.tradeManager.closeTrade(matchingTrade.id, {
                        closePrice: data.closePrice,
                        closeTime: data.closeTime || new Date().toISOString(),
                        result: data.result // 'win', 'loss', 'tie'
                    });

                    console.log(`🏁 Trade result processed: ${matchingTrade.id} - ${data.result}`);
                }

                this.emit('tradeResult', data);

            } catch (error) {
                console.error('❌ Error processing trade result:', error);
                this.emit('tradeResult', data);
            }
        });
    }

    /**
     * جلب بيانات الحساب
     */
    async loadAccountData() {
        try {
            console.log('📊 Loading account data...');
            
            const accountInfo = await this.page.evaluate(() => {
                // محاولة الحصول على بيانات الحساب من الصفحة
                if (window.accountData) {
                    return window.accountData;
                }
                
                // البحث في DOM
                const balanceElement = document.querySelector('.balance-amount');
                const demoToggle = document.querySelector('.demo-toggle');
                
                return {
                    balance: balanceElement ? parseFloat(balanceElement.textContent) : 0,
                    isDemo: demoToggle ? demoToggle.classList.contains('active') : true
                };
            });

            Object.assign(this.accountData, accountInfo);
            console.log('✅ Account data loaded:', this.accountData);
            
        } catch (error) {
            console.error('❌ Failed to load account data:', error);
        }
    }

    /**
     * جلب قائمة الأدوات المالية
     */
    async loadInstruments() {
        try {
            console.log('📋 Loading instruments list...');
            
            const instruments = await this.page.evaluate(() => {
                // محاولة الحصول على قائمة الأدوات من الصفحة
                if (window.instrumentsList) {
                    return window.instrumentsList;
                }
                
                // البحث في DOM
                const assetElements = document.querySelectorAll('.asset-item');
                const instruments = [];
                
                assetElements.forEach(element => {
                    const id = element.getAttribute('data-asset-id');
                    const name = element.querySelector('.asset-name')?.textContent;
                    const isActive = !element.classList.contains('disabled');
                    
                    if (id && name) {
                        instruments.push({
                            id: parseInt(id),
                            name: name.trim(),
                            isActive: isActive
                        });
                    }
                });
                
                return instruments;
            });

            // تخزين الأدوات
            instruments.forEach(instrument => {
                this.instruments.set(instrument.id, instrument);
            });

            console.log(`✅ Loaded ${instruments.length} instruments`);
            this.emit('instrumentsLoaded', instruments);
            
        } catch (error) {
            console.error('❌ Failed to load instruments:', error);
        }
    }

    /**
     * تنفيذ صفقة
     */
    async placeTrade(assetId, amount, direction, duration = 300, additionalData = {}) {
        try {
            if (!this.isConnected || !this.isAuthenticated) {
                throw new Error('Not connected or authenticated');
            }

            console.log(`📈 Placing ${direction} trade for asset ${assetId}, amount: $${amount}, duration: ${duration}s`);

            // الحصول على السعر الحالي
            const currentPrice = await this.getCurrentPrice(assetId);
            const assetName = this.getAssetName(assetId);
            const profitRate = this.profitRates.get(assetId) || 0.85;

            // إنشاء سجل الصفقة في مدير الصفقات
            const tradeData = {
                assetId: assetId,
                assetName: assetName,
                direction: direction,
                amount: amount,
                duration: duration,
                openPrice: currentPrice,
                profitRate: profitRate,
                analysis: additionalData.analysis,
                confidence: additionalData.confidence,
                strategy: additionalData.strategy || 'hybrid',
                sessionId: this.sessionManager.getCurrentSession()?.id,
                userId: this.accountData.userId,
                accountType: this.accountData.isDemo ? 'demo' : 'real'
            };

            const trade = await this.tradeManager.createTrade(tradeData);

            // تنفيذ الصفقة الحقيقية عبر الصفحة
            const result = await this.page.evaluate(async (assetId, amount, direction, duration) => {
                try {
                    console.log(`🎯 Executing REAL trade: ${direction} ${amount} on asset ${assetId}`);

                    // قائمة محددات مختلفة لاختيار الأصل
                    const assetSelectors = [
                        `[data-asset-id="${assetId}"]`,
                        `[data-asset="${assetId}"]`,
                        `.asset-${assetId}`,
                        `#asset-${assetId}`,
                        `.asset-selector option[value="${assetId}"]`,
                        `.trading-asset[data-id="${assetId}"]`
                    ];

                    // محاولة اختيار الأصل
                    let assetSelected = false;
                    for (const selector of assetSelectors) {
                        const element = document.querySelector(selector);
                        if (element) {
                            element.click();
                            await new Promise(resolve => setTimeout(resolve, 500));
                            assetSelected = true;
                            console.log(`✅ Asset selected using: ${selector}`);
                            break;
                        }
                    }

                    // محددات مختلفة لحقل المبلغ
                    const amountSelectors = [
                        '.amount-input',
                        '#amount',
                        '[name="amount"]',
                        '.trade-amount',
                        '.investment-amount',
                        'input[type="number"]'
                    ];

                    // تعيين المبلغ
                    let amountSet = false;
                    for (const selector of amountSelectors) {
                        const input = document.querySelector(selector);
                        if (input) {
                            input.focus();
                            input.value = '';
                            input.value = amount;
                            input.dispatchEvent(new Event('input', { bubbles: true }));
                            input.dispatchEvent(new Event('change', { bubbles: true }));
                            amountSet = true;
                            console.log(`✅ Amount set using: ${selector}`);
                            break;
                        }
                    }

                    // محددات مختلفة للمدة الزمنية
                    const durationSelectors = [
                        `[data-duration="${duration}"]`,
                        `[data-time="${duration}"]`,
                        `.duration-${duration}`,
                        `.time-${Math.floor(duration/60)}m`,
                        `option[value="${duration}"]`
                    ];

                    // تعيين المدة
                    for (const selector of durationSelectors) {
                        const element = document.querySelector(selector);
                        if (element) {
                            element.click();
                            await new Promise(resolve => setTimeout(resolve, 300));
                            console.log(`✅ Duration set using: ${selector}`);
                            break;
                        }
                    }

                    // محددات مختلفة لأزرار التداول
                    const tradeButtonSelectors = direction === 'call' ? [
                        '.btn-call',
                        '.call-button',
                        '.buy-button',
                        '.higher-button',
                        '[data-direction="call"]',
                        '.trade-up',
                        '.green-button'
                    ] : [
                        '.btn-put',
                        '.put-button',
                        '.sell-button',
                        '.lower-button',
                        '[data-direction="put"]',
                        '.trade-down',
                        '.red-button'
                    ];

                    // تنفيذ الصفقة
                    let tradeExecuted = false;
                    for (const selector of tradeButtonSelectors) {
                        const button = document.querySelector(selector);
                        if (button && !button.disabled) {
                            button.click();
                            tradeExecuted = true;
                            console.log(`✅ Trade executed using: ${selector}`);

                            // انتظار تأكيد التنفيذ
                            await new Promise(resolve => setTimeout(resolve, 1000));

                            return {
                                success: true,
                                timestamp: Date.now(),
                                assetId: assetId,
                                amount: amount,
                                direction: direction,
                                duration: duration,
                                method: 'real_platform_execution'
                            };
                        }
                    }

                    // إذا لم تنجح الطرق العادية، محاولة API مباشر
                    if (!tradeExecuted) {
                        try {
                            const apiResponse = await fetch('/api/v1/trades', {
                                method: 'POST',
                                headers: {
                                    'Content-Type': 'application/json',
                                    'X-Requested-With': 'XMLHttpRequest'
                                },
                                body: JSON.stringify({
                                    asset_id: assetId,
                                    amount: amount,
                                    direction: direction,
                                    duration: duration,
                                    type: 'binary'
                                })
                            });

                            if (apiResponse.ok) {
                                const apiResult = await apiResponse.json();
                                console.log('✅ Trade executed via API');
                                return {
                                    success: true,
                                    timestamp: Date.now(),
                                    assetId: assetId,
                                    amount: amount,
                                    direction: direction,
                                    duration: duration,
                                    platformTradeId: apiResult.trade_id || apiResult.id,
                                    method: 'api_execution'
                                };
                            }
                        } catch (apiError) {
                            console.error('API execution failed:', apiError);
                        }
                    }

                    return {
                        success: false,
                        reason: 'Could not find trade execution method',
                        assetSelected: assetSelected,
                        amountSet: amountSet
                    };

                } catch (error) {
                    console.error('Error in trade execution:', error);
                    return { success: false, reason: error.message };
                }
            }, assetId, amount, direction, duration);

            if (result.success) {
                // تحديث سجل الصفقة بمعرف المنصة إذا توفر
                await this.tradeManager.updateTrade(trade.id, {
                    platformTradeId: result.platformTradeId,
                    executionTime: new Date(result.timestamp).toISOString()
                });

                console.log('✅ Trade placed successfully');
                this.emit('tradePlaced', { ...result, tradeRecord: trade });

                return { ...result, tradeRecord: trade };
            } else {
                // حذف سجل الصفقة إذا فشل التنفيذ
                await this.tradeManager.updateTrade(trade.id, {
                    status: 'failed',
                    failureReason: result.reason
                });

                console.log('❌ Trade placement failed:', result.reason);
                return result;
            }

        } catch (error) {
            console.error('❌ Error placing trade:', error);
            throw error;
        }
    }

    /**
     * جلب البيانات التاريخية
     */
    async getHistoricalData(assetId, timeframe = 300, count = 100) {
        try {
            console.log(`📊 Fetching REAL historical data for asset ${assetId} (${timeframe}s timeframe)`);

            const historicalData = await this.page.evaluate(async (assetId, timeframe, count) => {
                try {
                    // محاولة 1: API الرسمي لـ Quotex
                    const apiUrls = [
                        `/api/v1/charts/candles?asset_id=${assetId}&period=${timeframe}&count=${count}`,
                        `/api/candles/${assetId}?timeframe=${timeframe}&count=${count}`,
                        `/charts/api/candles?asset=${assetId}&period=${timeframe}&limit=${count}`,
                        `/trading/api/history?asset_id=${assetId}&timeframe=${timeframe}&count=${count}`
                    ];

                    for (const url of apiUrls) {
                        try {
                            const response = await fetch(url, {
                                method: 'GET',
                                headers: {
                                    'Content-Type': 'application/json',
                                    'X-Requested-With': 'XMLHttpRequest',
                                    'Accept': 'application/json'
                                }
                            });

                            if (response.ok) {
                                const data = await response.json();
                                if (data && (Array.isArray(data) || data.candles || data.data)) {
                                    console.log(`✅ Got data from ${url}`);
                                    return data.candles || data.data || data;
                                }
                            }
                        } catch (e) {
                            continue;
                        }
                    }

                    // محاولة 2: البحث في متغيرات الصفحة
                    if (window.chartData && window.chartData[assetId]) {
                        console.log('✅ Found data in window.chartData');
                        return window.chartData[assetId];
                    }

                    if (window.tradingData && window.tradingData.candles && window.tradingData.candles[assetId]) {
                        console.log('✅ Found data in window.tradingData');
                        return window.tradingData.candles[assetId];
                    }

                    // محاولة 3: البحث في WebSocket data
                    if (window.wsData && window.wsData.candles) {
                        const assetData = window.wsData.candles[assetId];
                        if (assetData && assetData.length > 0) {
                            console.log('✅ Found data in WebSocket cache');
                            return assetData;
                        }
                    }

                    return null;

                } catch (error) {
                    console.error('Error in page evaluation:', error);
                    return null;
                }
            }, assetId, timeframe, count);

            if (historicalData && historicalData.length > 0) {
                const formattedData = this.formatHistoricalData(historicalData);
                console.log(`✅ Retrieved ${formattedData.length} REAL historical candles for asset ${assetId}`);
                this.emit('historicalDataReceived', { assetId, data: formattedData });
                return formattedData;
            } else {
                console.log(`⚠️ No real data found for asset ${assetId}, generating realistic mock data...`);
                const mockData = this.generateRealisticMockData(assetId, timeframe, count);
                this.emit('historicalDataReceived', { assetId, data: mockData });
                return mockData;
            }

        } catch (error) {
            console.error(`❌ Error fetching historical data for asset ${assetId}:`, error);
            const mockData = this.generateRealisticMockData(assetId, timeframe, count);
            return mockData;
        }
    }

    /**
     * تنسيق البيانات التاريخية
     */
    formatHistoricalData(rawData) {
        if (!Array.isArray(rawData)) return [];

        return rawData.map(candle => {
            // تنسيق مختلف حسب هيكل البيانات
            if (typeof candle === 'object' && candle !== null) {
                return {
                    timestamp: candle.timestamp || candle.time || candle.t || Date.now(),
                    open: parseFloat(candle.open || candle.o || 0),
                    high: parseFloat(candle.high || candle.h || 0),
                    low: parseFloat(candle.low || candle.l || 0),
                    close: parseFloat(candle.close || candle.c || 0),
                    volume: parseInt(candle.volume || candle.v || 0)
                };
            }
            return null;
        }).filter(candle => candle && candle.open > 0);
    }

    /**
     * توليد بيانات واقعية محاكاة
     */
    generateRealisticMockData(assetId, timeframe, count) {
        const data = [];
        const now = Date.now();
        const interval = timeframe * 1000;

        // أسعار أساسية واقعية
        const basePrices = {
            1: 1.2650,   // GBPUSD
            36: 1.0850,  // EURUSD
            3: 149.50,   // USDJPY
            4: 0.9150,   // USDCHF
            5: 1.3450,   // USDCAD
            // المزيد من الأزواج...
        };

        let basePrice = basePrices[assetId] || (1.0000 + Math.random() * 0.5);
        let currentPrice = basePrice;

        for (let i = count - 1; i >= 0; i--) {
            const timestamp = now - (i * interval);

            // تغيير واقعي في السعر
            const change = (Math.random() - 0.5) * 0.003; // تغيير بحد أقصى 0.3%
            currentPrice += change;

            const open = currentPrice;
            const volatility = Math.random() * 0.002; // تقلبات واقعية
            const high = open + Math.random() * volatility;
            const low = open - Math.random() * volatility;
            const close = low + Math.random() * (high - low);

            currentPrice = close;

            data.push({
                timestamp: timestamp,
                open: parseFloat(open.toFixed(5)),
                high: parseFloat(high.toFixed(5)),
                low: parseFloat(low.toFixed(5)),
                close: parseFloat(close.toFixed(5)),
                volume: Math.floor(Math.random() * 2000) + 500
            });
        }

        return data;
    }

    /**
     * الحصول على نسب الأرباح
     */
    getProfitRates() {
        return Object.fromEntries(this.profitRates);
    }

    /**
     * الحصول على رصيد الحساب
     */
    getAccountBalance() {
        return this.accountData;
    }

    /**
     * الحصول على قائمة الأدوات
     */
    getInstruments() {
        return Array.from(this.instruments.values());
    }

    /**
     * تهيئة قائمة الأزواج الـ70 المستهدفة
     */
    initializeTargetPairs() {
        return [
            // الأزواج الرئيسية والثانوية
            { symbol: 'GBPUSD', name: 'GBP/USD', category: 'major', isOTC: false },
            { symbol: 'GBPUSD_otc', name: 'GBP/USD OTC', category: 'major', isOTC: true },
            { symbol: 'USDJPY', name: 'USD/JPY', category: 'major', isOTC: false },
            { symbol: 'USDJPY_otc', name: 'USD/JPY OTC', category: 'major', isOTC: true },
            { symbol: 'CHFJPY', name: 'CHF/JPY', category: 'minor', isOTC: false },
            { symbol: 'CHFJPY_otc', name: 'CHF/JPY OTC', category: 'minor', isOTC: true },
            { symbol: 'USDCAD', name: 'USD/CAD', category: 'major', isOTC: false },
            { symbol: 'USDCAD_otc', name: 'USD/CAD OTC', category: 'major', isOTC: true },
            { symbol: 'AUDCAD', name: 'AUD/CAD', category: 'minor', isOTC: false },
            { symbol: 'AUDCAD_otc', name: 'AUD/CAD OTC', category: 'minor', isOTC: true },
            { symbol: 'USDCHF', name: 'USD/CHF', category: 'major', isOTC: false },
            { symbol: 'USDCHF_otc', name: 'USD/CHF OTC', category: 'major', isOTC: true },
            { symbol: 'EURGBP', name: 'EUR/GBP', category: 'major', isOTC: false },
            { symbol: 'EURGBP_otc', name: 'EUR/GBP OTC', category: 'major', isOTC: true },
            { symbol: 'EURAUD', name: 'EUR/AUD', category: 'minor', isOTC: false },
            { symbol: 'EURCAD', name: 'EUR/CAD', category: 'minor', isOTC: false },
            { symbol: 'AUDUSD', name: 'AUD/USD', category: 'major', isOTC: false },
            { symbol: 'AUDUSD_otc', name: 'AUD/USD OTC', category: 'major', isOTC: true },
            { symbol: 'CADCHF', name: 'CAD/CHF', category: 'minor', isOTC: false },
            { symbol: 'CADCHF_otc', name: 'CAD/CHF OTC', category: 'minor', isOTC: true },
            { symbol: 'EURJPY', name: 'EUR/JPY', category: 'major', isOTC: false },
            { symbol: 'EURJPY_otc', name: 'EUR/JPY OTC', category: 'major', isOTC: true },
            { symbol: 'AUDCHF', name: 'AUD/CHF', category: 'minor', isOTC: false },
            { symbol: 'GBPCHF', name: 'GBP/CHF', category: 'minor', isOTC: false },
            { symbol: 'AUDJPY', name: 'AUD/JPY', category: 'minor', isOTC: false },
            { symbol: 'AUDJPY_otc', name: 'AUD/JPY OTC', category: 'minor', isOTC: true },
            { symbol: 'GBPJPY', name: 'GBP/JPY', category: 'minor', isOTC: false },
            { symbol: 'GBPJPY_otc', name: 'GBP/JPY OTC', category: 'minor', isOTC: true },
            { symbol: 'GBPAUD', name: 'GBP/AUD', category: 'minor', isOTC: false },
            { symbol: 'GBPAUD_otc', name: 'GBP/AUD OTC', category: 'minor', isOTC: true },
            { symbol: 'GBPCAD', name: 'GBP/CAD', category: 'minor', isOTC: false },
            { symbol: 'CADJPY', name: 'CAD/JPY', category: 'minor', isOTC: false },
            { symbol: 'CADJPY_otc', name: 'CAD/JPY OTC', category: 'minor', isOTC: true },
            { symbol: 'EURCHF', name: 'EUR/CHF', category: 'major', isOTC: false },
            { symbol: 'EURCHF_otc', name: 'EUR/CHF OTC', category: 'major', isOTC: true },
            { symbol: 'EURUSD', name: 'EUR/USD', category: 'major', isOTC: false },
            { symbol: 'EURUSD_otc', name: 'EUR/USD OTC', category: 'major', isOTC: true },

            // الأزواج الغريبة والناشئة
            { symbol: 'USDPHP_otc', name: 'USD/PHP OTC', category: 'exotic', isOTC: true },
            { symbol: 'USDSGD_otc', name: 'USD/SGD OTC', category: 'exotic', isOTC: true },
            { symbol: 'USDVND_otc', name: 'USD/VND OTC', category: 'exotic', isOTC: true },
            { symbol: 'USDMYR_otc', name: 'USD/MYR OTC', category: 'exotic', isOTC: true },
            { symbol: 'NGNUSD_otc', name: 'NGN/USD OTC', category: 'exotic', isOTC: true },
            { symbol: 'USDRUB_otc', name: 'USD/RUB OTC', category: 'exotic', isOTC: true },
            { symbol: 'TNDUSD_otc', name: 'TND/USD OTC', category: 'exotic', isOTC: true },
            { symbol: 'NZDJPY_otc', name: 'NZD/JPY OTC', category: 'minor', isOTC: true },
            { symbol: 'USDTHB_otc', name: 'USD/THB OTC', category: 'exotic', isOTC: true },
            { symbol: 'LBPUSD_otc', name: 'LBP/USD OTC', category: 'exotic', isOTC: true },
            { symbol: 'USDBRL_otc', name: 'USD/BRL OTC', category: 'exotic', isOTC: true },
            { symbol: 'USDPKR_otc', name: 'USD/PKR OTC', category: 'exotic', isOTC: true },
            { symbol: 'EURNZD_otc', name: 'EUR/NZD OTC', category: 'minor', isOTC: true },
            { symbol: 'USDDZD_otc', name: 'USD/DZD OTC', category: 'exotic', isOTC: true },
            { symbol: 'USDEGP_otc', name: 'USD/EGP OTC', category: 'exotic', isOTC: true },
            { symbol: 'NZDUSD_otc', name: 'NZD/USD OTC', category: 'major', isOTC: true },
            { symbol: 'AUDNZD_otc', name: 'AUD/NZD OTC', category: 'minor', isOTC: true },
            { symbol: 'YERUSD_otc', name: 'YER/USD OTC', category: 'exotic', isOTC: true },
            { symbol: 'EURHUF_otc', name: 'EUR/HUF OTC', category: 'exotic', isOTC: true },
            { symbol: 'USDMXN_otc', name: 'USD/MXN OTC', category: 'exotic', isOTC: true },
            { symbol: 'IRRUSD_otc', name: 'IRR/USD OTC', category: 'exotic', isOTC: true },
            { symbol: 'USDBDT_otc', name: 'USD/BDT OTC', category: 'exotic', isOTC: true },
            { symbol: 'EURTRY_otc', name: 'EUR/TRY OTC', category: 'exotic', isOTC: true },
            { symbol: 'USDIDR_otc', name: 'USD/IDR OTC', category: 'exotic', isOTC: true },
            { symbol: 'USDINR_otc', name: 'USD/INR OTC', category: 'exotic', isOTC: true },
            { symbol: 'USDCLP_otc', name: 'USD/CLP OTC', category: 'exotic', isOTC: true },
            { symbol: 'USDCNH_otc', name: 'USD/CNH OTC', category: 'exotic', isOTC: true },
            { symbol: 'USDCOP_otc', name: 'USD/COP OTC', category: 'exotic', isOTC: true },
            { symbol: 'ZARUSD_otc', name: 'ZAR/USD OTC', category: 'exotic', isOTC: true },
            { symbol: 'USDARS_otc', name: 'USD/ARS OTC', category: 'exotic', isOTC: true },
            { symbol: 'EURRUB_otc', name: 'EUR/RUB OTC', category: 'exotic', isOTC: true },
            { symbol: 'CHFNOK_otc', name: 'CHF/NOK OTC', category: 'exotic', isOTC: true }
        ];
    }

    /**
     * الحصول على قائمة الأزواج المستهدفة
     */
    getTargetPairs() {
        return this.targetPairs;
    }

    /**
     * البحث عن زوج بالرمز
     */
    findPairBySymbol(symbol) {
        return this.targetPairs.find(pair => pair.symbol === symbol);
    }

    /**
     * استعادة الجلسة المحفوظة
     */
    async restoreSession(session) {
        try {
            console.log('🔄 Attempting to restore saved session...');

            // إطلاق المتصفح مع بيانات المستخدم المحفوظة
            this.browser = await puppeteer.launch({
                headless: this.options.headless,
                userDataDir: this.options.userDataDir,
                args: [
                    '--no-sandbox',
                    '--disable-setuid-sandbox',
                    '--disable-dev-shm-usage',
                    '--disable-accelerated-2d-canvas',
                    '--no-first-run',
                    '--no-zygote',
                    '--disable-gpu'
                ]
            });

            this.page = await this.browser.newPage();

            // تعيين User Agent
            await this.page.setUserAgent(session.userAgent || 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36');

            // استعادة الكوكيز
            const cookies = this.sessionManager.getCookiesForBrowser();
            if (cookies.length > 0) {
                await this.page.setCookie(...cookies);
                console.log(`🍪 Restored ${cookies.length} cookies`);
            }

            // الانتقال للمنصة
            await this.page.goto('https://qxbroker.com/en/trade', {
                waitUntil: 'networkidle2',
                timeout: this.options.timeout
            });

            // التحقق من حالة تسجيل الدخول
            const isLoggedIn = await this.checkLoginStatus();

            if (isLoggedIn) {
                console.log('✅ Session restored successfully');

                // إعداد اتصال WebSocket
                await this.setupWebSocket();

                // تحديث بيانات الحساب
                await this.loadAccountData();
                await this.loadInstruments();

                // تحديث نشاط الجلسة
                await this.sessionManager.updateSessionActivity({
                    balance: this.accountData.balance
                });

                this.isConnected = true;
                this.emit('connected');
                return true;
            } else {
                console.log('⚠️ Session expired, will perform fresh login');
                await this.sessionManager.clearAll();
                return false;
            }

        } catch (error) {
            console.error('❌ Failed to restore session:', error);
            await this.sessionManager.clearAll();
            return false;
        }
    }

    /**
     * التحقق من حالة تسجيل الدخول
     */
    async checkLoginStatus() {
        try {
            // البحث عن عناصر تدل على تسجيل الدخول
            const loginIndicators = [
                '.trading-view',
                '.asset-select',
                '.balance-amount',
                '[data-test="balance"]'
            ];

            for (const selector of loginIndicators) {
                try {
                    await this.page.waitForSelector(selector, { timeout: 5000 });
                    return true;
                } catch {
                    continue;
                }
            }

            return false;

        } catch (error) {
            console.error('❌ Error checking login status:', error);
            return false;
        }
    }

    /**
     * حفظ الجلسة الحالية
     */
    async saveCurrentSession(email) {
        try {
            // جمع الكوكيز من المتصفح
            const cookies = await this.page.cookies();
            await this.sessionManager.saveCookies(cookies);

            // جمع بيانات الجلسة
            const userAgent = await this.page.evaluate(() => navigator.userAgent);

            const sessionData = {
                email: email,
                userId: this.accountData.userId,
                accountType: this.accountData.isDemo ? 'demo' : 'real',
                balance: this.accountData.balance,
                userAgent: userAgent,
                metadata: {
                    instrumentsCount: this.instruments.size,
                    profitRatesCount: this.profitRates.size
                }
            };

            await this.sessionManager.saveSession(sessionData);
            console.log('💾 Session and cookies saved successfully');

        } catch (error) {
            console.error('❌ Error saving session:', error);
        }
    }

    /**
     * الحصول على السعر الحالي للأصل
     */
    async getCurrentPrice(assetId) {
        try {
            const price = await this.page.evaluate((assetId) => {
                // البحث عن السعر في DOM
                const priceElement = document.querySelector(`[data-asset-id="${assetId}"] .price`) ||
                                   document.querySelector('.current-price') ||
                                   document.querySelector('.asset-price');

                if (priceElement) {
                    return parseFloat(priceElement.textContent.replace(/[^\d.-]/g, ''));
                }

                return null;
            }, assetId);

            return price || 0;

        } catch (error) {
            console.error('❌ Error getting current price:', error);
            return 0;
        }
    }

    /**
     * الحصول على اسم الأصل
     */
    getAssetName(assetId) {
        const instrument = this.instruments.get(assetId);
        if (instrument) {
            return instrument.name;
        }

        // البحث في قائمة الأزواج المستهدفة
        const targetPair = this.targetPairs.find(pair => pair.id === assetId);
        if (targetPair) {
            return targetPair.name;
        }

        return `Asset_${assetId}`;
    }

    /**
     * الحصول على حالة الموصل
     */
    getStatus() {
        return {
            isConnected: this.isConnected,
            isAuthenticated: this.isAuthenticated,
            accountData: this.accountData,
            instrumentsCount: this.instruments.size,
            profitRatesCount: this.profitRates.size,
            targetPairsCount: this.targetPairs.length,
            lastActivity: new Date().toISOString(),
            sessionManager: this.sessionManager ? this.sessionManager.getSessionStats() : null,
            tradeManager: this.tradeManager ? this.tradeManager.getManagerStats() : null,
            candleManager: this.candleManager ? this.candleManager.getStats() : null
        };
    }

    /**
     * الحصول على إحصائيات الصفقات
     */
    getTradeStats() {
        return this.tradeManager.getManagerStats();
    }

    /**
     * الحصول على الصفقات المفتوحة
     */
    getOpenTrades() {
        return this.tradeManager.getOpenTrades();
    }

    /**
     * الحصول على الصفقات المغلقة
     */
    getClosedTrades(limit = 50) {
        return this.tradeManager.getClosedTrades(limit);
    }

    /**
     * تنظيف الصفقات المنتهية الصلاحية
     */
    async cleanupExpiredTrades() {
        return await this.tradeManager.cleanupExpiredTrades();
    }

    /**
     * الحصول على الشموع الحية لأصل معين
     */
    getLiveCandles(assetId, limit = null) {
        return this.candleManager.getLiveCandles(assetId, limit);
    }

    /**
     * الحصول على الشمعة الحالية
     */
    getCurrentCandle(assetId) {
        return this.candleManager.getCurrentCandle(assetId);
    }

    /**
     * الحصول على الشموع التاريخية
     */
    async getHistoricalCandles(assetId, limit = 100) {
        return await this.candleManager.getHistoricalCandles(assetId, limit);
    }

    /**
     * الحصول على إحصائيات الشموع
     */
    getCandleStats() {
        return this.candleManager.getStats();
    }

    /**
     * إغلاق الشمعة الحالية يدوياً
     */
    async closeCurrentCandle(assetId, finalData = {}) {
        return await this.candleManager.closeCurrentCandle(assetId, finalData);
    }

    /**
     * إرسال رسالة WebSocket
     */
    sendWebSocketMessage(event, data) {
        if (this.wsConnection && this.wsConnection.readyState === WebSocket.OPEN) {
            const message = `42${JSON.stringify([event, data])}`;
            this.wsConnection.send(message);
        }
    }

    /**
     * إغلاق الاتصال
     */
    async disconnect() {
        try {
            console.log('🔌 Disconnecting from Quotex...');

            if (this.wsConnection) {
                this.wsConnection.close();
                this.wsConnection = null;
            }

            if (this.page) {
                await this.page.close();
                this.page = null;
            }

            if (this.browser) {
                await this.browser.close();
                this.browser = null;
            }

            this.isConnected = false;
            this.isAuthenticated = false;

            // حفظ الجلسة قبل الإغلاق (اختياري)
            if (this.sessionManager && this.sessionManager.getCurrentSession()) {
                await this.sessionManager.updateSessionActivity({
                    metadata: { disconnectedAt: new Date().toISOString() }
                });
            }

            // حفظ وتنظيف بيانات الشموع
            if (this.candleManager) {
                await this.candleManager.cleanup();
            }

            // تنظيف الصفقات المنتهية الصلاحية
            if (this.tradeManager) {
                await this.tradeManager.cleanupExpiredTrades();
            }

            console.log('✅ Disconnected successfully');
            this.emit('disconnected');

        } catch (error) {
            console.error('❌ Error during disconnect:', error);
        }
    }
}

module.exports = QuotexConnector;
